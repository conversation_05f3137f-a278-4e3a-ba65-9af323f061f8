export interface PerplexityGenerateInput {
  sesid: string;
  preset: string;
  query: string;
  query_args: {
    query_id?: string;
    [key: string]: any;
  };
}

export interface PerplexityGenerateResponse {
  ok: boolean;
  id: {
    ses: string;
    corr: string;
  };
  input: string;
  output: string;
  sizes: {
    completion_tokens: number;
    prompt_tokens: number;
    total_tokens: number;
  };
  errcode?: string;
  message?: string;
}

export interface PerplexityResetResponse {
  ok: boolean;
  message?: string;
  errcode?: string;
}

export interface PerplexitySelectedPresetResponse {
  ok: boolean;
  preset: {
    id: string;
    name: string;
    description?: string;
    [key: string]: any;
  };
  errcode?: string;
  message?: string;
}

export interface PerplexityPresetResponse {
  ok: boolean;
  presets: Array<{
    id: string;
    name: string;
    description?: string;
    [key: string]: any;
  }>;
  errcode?: string;
  message?: string;
}
