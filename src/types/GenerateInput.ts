export interface GenerateInput {
  id?: GenerateId;
  preset: string;
  query: string;
  query_args?: QueryArgs;
  prompt_params?: PromptParams;
  model_params?: ModelParams;
}

export interface GenerateId {
  clt?: string;
  ses?: string;
  corr?: string;
}

export interface QueryArgs {
  query_id?: string;
  fields?: Record<string, string | number | boolean | null>;
  media?: Media;
}

export type Media = { url: string } | { data: string };

export interface PromptParams {
  preamble?: string | string[];
  examples?: string[];
  template?: string;
}

export interface ModelParams {
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  [key: string]: unknown;
}
