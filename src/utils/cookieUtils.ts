// Cookie consent key for localStorage
export const COOKIE_CONSENT_KEY = "genigma_cookie_consent";

/**
 * Check if cookies have been accepted previously
 * @returns True if cookies have been accepted, false otherwise
 */
export const checkCookieConsent = (): boolean => {
  try {
    const consent = localStorage.getItem(COOKIE_CONSENT_KEY);
    return consent === "accepted";
  } catch (error) {
    console.warn("⚠️ Error checking cookie consent:", error);
    return false;
  }
};

/**
 * Save cookie consent to localStorage
 * @returns True if saved successfully, false otherwise
 */
export const saveCookieConsent = (): boolean => {
  try {
    localStorage.setItem(COOKIE_CONSENT_KEY, "accepted");
    return true;
  } catch (error) {
    console.error("❌ Error saving cookie consent:", error);
    return false;
  }
};

// /**
//  * Clear cookie consent from localStorage
//  * Useful for testing or user preference reset
//  * @returns True if cleared successfully, false otherwise
//  */
// export const clearCookieConsent = (): boolean => {
//   try {
//     localStorage.removeItem(COOKIE_CONSENT_KEY);
//     return true;
//   } catch (error) {
//     console.error("❌ Error clearing cookie consent:", error);
//     return false;
//   }
// };

// /**
//  * Get cookie consent status as string
//  * @returns 'accepted', 'not_set', or 'error'
//  */
// export const getCookieConsentStatus = (): "accepted" | "not_set" | "error" => {
//   try {
//     const consent = localStorage.getItem(COOKIE_CONSENT_KEY);
//     if (consent === "accepted") {
//       return "accepted";
//     }
//     return "not_set";
//   } catch (error) {
//     console.warn("⚠️ Error getting cookie consent status:", error);
//     return "error";
//   }
// };
