import type { StoredMessage } from "../services/ConversationStorage";

/**
 * Utility functions for conversation management
 */

export interface ConversationMessage {
  id: string;
  type: "user" | "ai";
  content: string;
  timestamp: Date;
  isInterim?: boolean;
}

/**
 * Convert StoredMessage to ConversationMessage format
 * @param stored - The stored message from storage
 * @returns Converted conversation message
 */
export const convertStoredToConversation = (
  stored: StoredMessage
): ConversationMessage => ({
  id: stored.id,
  type: stored.role === "user" ? "user" : "ai",
  content: stored.content,
  timestamp: new Date(stored.createdAt),
  isInterim: false,
});

/**
 * Extract response text from API response object
 * Handles multiple possible response field names
 * @param response - API response object
 * @param fallback - Default text if no response found
 * @returns Extracted response text
 */
export const extractResponseText = (response: any, fallback = "Respuesta no encontrada"): string => {
  return response.response || 
         response.output || 
         response.result || 
         response.text || 
         response.content || 
         fallback;
};

/**
 * Check if transcript should be ignored (likely AI echo)
 * @param transcript - The speech recognition transcript
 * @returns True if transcript should be ignored
 */
export const shouldIgnoreTranscript = (transcript: string): boolean => {
  const ignorePhrases = [
    "ya estoy pensando en un personaje",
    "soy un personaje",
    "adivina quién soy"
  ];
  
  const lowerTranscript = transcript.toLowerCase();
  return ignorePhrases.some(phrase => lowerTranscript.includes(phrase));
};

/**
 * Generate unique message ID
 * @param type - Message type (user or ai)
 * @returns Unique message ID
 */
export const generateMessageId = (type: "user" | "ai"): string => {
  return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Validate message content
 * @param content - Message content to validate
 * @returns True if content is valid
 */
export const isValidMessageContent = (content: string): boolean => {
  return content && content.trim().length > 0;
};

/**
 * Create a new conversation message
 * @param type - Message type
 * @param content - Message content
 * @param isInterim - Whether this is an interim message
 * @returns New conversation message
 */
export const createConversationMessage = (
  type: "user" | "ai",
  content: string,
  isInterim = false
): ConversationMessage => ({
  id: generateMessageId(type),
  type,
  content,
  timestamp: new Date(),
  isInterim,
});

/**
 * Filter out interim messages of a specific type
 * @param messages - Array of messages
 * @param type - Type to filter out interim messages for
 * @returns Filtered messages array
 */
export const filterInterimMessages = (
  messages: ConversationMessage[],
  type: "user" | "ai"
): ConversationMessage[] => {
  return messages.filter(m => !m.isInterim || m.type !== type);
};
