import type { GenerateResponse } from "../types/GenerateResponse";

/**
 * Utility functions for API operations and response handling
 */

/**
 * Extract response text from API response object
 * Handles multiple possible response field names in priority order
 * @param response - API response object
 * @param fallback - Default text if no response found
 * @returns Extracted response text
 */
export const extractApiResponseText = (response: GenerateResponse, fallback = "Respuesta no encontrada"): string => {
  return response.output ||
         response.response ||
         response.result ||
         response.text ||
         response.content ||
         fallback;
};

/**
 * Parse JSON from response text if present
 * @param responseText - Raw response text that might contain JSON
 * @returns Parsed object with audioText and displayText, or null if no valid JSON
 */
export const parseJsonFromResponse = (responseText: string): { audioText: string; displayText: string } | null => {
  if (!responseText.trim().startsWith("{")) {
    return null;
  }

  try {
    // Find the end of JSON (first '}' that closes the main object)
    let jsonEndIndex = -1;
    let braceCount = 0;

    for (let i = 0; i < responseText.length; i++) {
      if (responseText[i] === '{') {
        braceCount++;
      } else if (responseText[i] === '}') {
        braceCount--;
        if (braceCount === 0) {
          jsonEndIndex = i;
          break;
        }
      }
    }

    if (jsonEndIndex === -1) {
      return null;
    }

    const jsonStr = responseText.substring(0, jsonEndIndex + 1);
    const parsed = JSON.parse(jsonStr);

    return {
      audioText: parsed.respuesta || parsed.audio_text || parsed.audioText || parsed.text || responseText,
      displayText: parsed.respuesta || parsed.display_text || parsed.displayText || parsed.text || responseText
    };
  } catch (error) {
    console.warn("⚠️ Error parsing JSON from response:", error);
    return null;
  }
};

/**
 * Process response text for audio and display
 * Handles both JSON and plain text responses
 * @param responseText - Raw response text
 * @returns Object with audioText and displayText
 */
export const processResponseText = (responseText: string): { audioText: string; displayText: string } => {
  const parsed = parseJsonFromResponse(responseText);

  if (parsed) {
    return parsed;
  }

  // Plain text response
  return {
    audioText: responseText,
    displayText: responseText
  };
};

/**
 * Create HTTP headers for API requests
 * @param apiKey - API key for authentication
 * @param sessionId - Session ID for the request
 * @returns Headers object
 */
export const createApiHeaders = (apiKey: string, sessionId: string): Record<string, string> => {
  return {
    "Access-Control-Allow-Origin": "*",
    "Content-Type": "application/json",
    "X-Api-Key": apiKey || "",
    "X-MG-Ses": sessionId,
    "X-Frame-Options": "SAMEORIGIN",
  };
};

/**
 * Handle API request with consistent error handling and logging
 * @param requestPromise - The axios request promise
 * @param isDevelopment - Whether in development mode for logging
 * @returns Promise with response data
 */
export const handleApiRequest = async <T>(
  requestPromise: Promise<any>,
  isDevelopment = false
): Promise<T> => {
  try {
    const res = await requestPromise;

    if (isDevelopment) {
      console.log(`🟢 ${res.status} ${res.config.url}`, res.data);
    }

    return res.data as T;
  } catch (error) {
    if (isDevelopment) {
      if (error instanceof Error) {
        console.error(`🔴 API ERROR: ${error.message}`);
      } else {
        console.error("🔴 API ERROR: Unknown error", error);
      }
    }

    throw new Error("Error al realizar la solicitud a la API");
  }
};

/**
 * Validate session ID
 * @param sessionId - Session ID to validate
 * @returns True if valid
 */
export const isValidSessionId = (sessionId: string): boolean => {
  return typeof sessionId === "string" && sessionId.trim().length > 0;
};

/**
 * Generate a fallback session ID if none exists
 * @returns Generated session ID
 */
export const generateFallbackSessionId = (): string => {
  return `fallback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
