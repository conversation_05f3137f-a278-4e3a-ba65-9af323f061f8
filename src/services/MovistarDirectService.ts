import axios from 'axios';
import type { IMovistarDirectService } from './impl/IMovistarDirectService';

// Obtener la URL base desde las variables de entorno
const BASE_URL = import.meta.env.VITE_MOVISTAR_API_URL || "https://ottcache.dof6.com/movistarplus/remote/contents";

export interface MovieResult {
  poster: string | null;
  ageRating: string | null;
  genre: string | null;
  title: string | null;
  synopsis: string | null;
}

export interface SearchParams {
  phrase: string;
  genre: string;
  filter: string;
  mode: string;
}

/**
 * Servicio directo para la API de Movistar+ OTT
 * Este servicio hace llamadas directas a la API sin usar MCP
 */
export class MovistarDirectService implements IMovistarDirectService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = BASE_URL;
  }

  /**
   * Buscar películas por actor
   * @param actor Nombre del actor a buscar
   * @returns Promise con los resultados de la búsqueda
   */
  async searchByActor(actor: string): Promise<string> {
    const params: SearchParams = {
      phrase: actor,
      genre: "Cine",
      filter: "BU-ACTOR",
      mode: "VODREJILLA"
    };

    try {
      const response = await axios.get(`${this.baseUrl}/search`, { params });
      const data = response.data.Contenidos;

      if (!data || !Array.isArray(data) || data.length === 0) {
        return `No se encontraron resultados para el actor "${actor}".`;
      }

      const fichas = await Promise.all(
        data.map(async (item: any) => {
          const fichaUrl = item.DatosEditoriales?.Ficha;
          if (!fichaUrl) return null;

          try {
            const fichaResp = await axios.get(fichaUrl);
            const f = fichaResp.data;
            return {
              poster: f.Imagen || f.Imagenes?.[0]?.uri || null,
              ageRating: f.NivelMoral?.Id || null,
              genre: f.Genero?.ComAntena || null,
              title: f.Titulo || null,
              synopsis: f.Sinopsis || null
            };
          } catch {
            return null;
          }
        })
      );

      const resultados = fichas.filter(Boolean) as MovieResult[];

      if (resultados.length === 0) {
        return `No se pudieron obtener detalles de las películas para el actor "${actor}".`;
      }

      // Procesar los resultados y generar respuesta
      return this.formatMovieResults(resultados, `actor "${actor}"`);

    } catch (error) {
      console.error('Error buscando por actor:', error);
      return `Error al buscar películas del actor "${actor}". Por favor, inténtalo de nuevo.`;
    }
  }

  /**
   * Buscar películas por título
   * @param title Título de la película a buscar
   * @returns Promise con los resultados de la búsqueda
   */
  async searchByTitle(title: string): Promise<string> {
    const params: SearchParams = {
      phrase: title,
      genre: "Cine",
      filter: "BU-TITULO",
      mode: "VODREJILLA"
    };

    try {
      const response = await axios.get(`${this.baseUrl}/search`, { params });
      const data = response.data.Contenidos;

      if (!data || !Array.isArray(data) || data.length === 0) {
        return `No se encontraron resultados para el título "${title}".`;
      }

      const fichas = await Promise.all(
        data.map(async (item: any) => {
          const fichaUrl = item.DatosEditoriales?.Ficha;
          if (!fichaUrl) return null;

          try {
            const fichaResp = await axios.get(fichaUrl);
            const f = fichaResp.data;
            return {
              poster: f.Imagen || f.Imagenes?.[0]?.uri || null,
              ageRating: f.NivelMoral?.Id || null,
              genre: f.Genero?.ComAntena || null,
              title: f.Titulo || null,
              synopsis: f.Sinopsis || null
            };
          } catch {
            return null;
          }
        })
      );

      const resultados = fichas.filter(Boolean) as MovieResult[];

      if (resultados.length === 0) {
        return `No se pudieron obtener detalles de las películas para el título "${title}".`;
      }

      // Procesar los resultados y generar respuesta
      return this.formatMovieResults(resultados, `título "${title}"`);

    } catch (error) {
      console.error('Error buscando por título:', error);
      return `Error al buscar películas con el título "${title}". Por favor, inténtalo de nuevo.`;
    }
  }

  /**
   * Formatear los resultados de películas en una respuesta legible
   * @param resultados Array de resultados de películas
   * @param searchTerm Término de búsqueda para incluir en la respuesta
   * @returns String formateado con los resultados
   */
  private formatMovieResults(resultados: MovieResult[], searchTerm: string): string {
    const contentBlocks: string[] = [];

    contentBlocks.push(`Se encontraron ${resultados.length} película(s) para ${searchTerm}:\n`);

    for (const ficha of resultados) {
      const movieInfo: string[] = [];

      if (ficha.title) {
        movieInfo.push(`🎬 **${ficha.title}**`);
      }

      if (ficha.genre) {
        movieInfo.push(`📂 Género: ${ficha.genre}`);
      }

      if (ficha.ageRating) {
        movieInfo.push(`🔞 Clasificación: ${ficha.ageRating}`);
      }

      if (ficha.synopsis) {
        movieInfo.push(`📝 Sinopsis: ${ficha.synopsis}`);
      }

      if (ficha.poster) {
        movieInfo.push(`🖼️ Poster disponible`);
      }

      if (movieInfo.length > 0) {
        contentBlocks.push(movieInfo.join('\n') + '\n');
      }
    }

    return contentBlocks.join('\n');
  }

  /**
   * Obtener la URL base configurada
   * @returns URL base del servicio
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * Verificar si el servicio está configurado correctamente
   * @returns true si la configuración es válida
   */
  isConfigured(): boolean {
    return !!this.baseUrl && this.baseUrl.trim() !== '';
  }
}
