/**
 * Conversation State Machine
 * Enforces valid state transitions and prevents race conditions
 */

import type { ConversationState } from './impl/ISpeechRecognitionService';

export interface StateTransition {
  from: ConversationState;
  to: ConversationState;
  condition?: () => boolean;
  action?: () => void | Promise<void>;
}

export interface StateMachineConfig {
  initialState: ConversationState;
  transitions: StateTransition[];
  onStateChange?: (from: ConversationState, to: ConversationState) => void;
  onInvalidTransition?: (from: ConversationState, to: ConversationState) => void;
}

export class ConversationStateMachine {
  private currentState: ConversationState;
  private transitions: Map<string, StateTransition[]> = new Map();
  private onStateChange?: (from: ConversationState, to: ConversationState) => void;
  private onInvalidTransition?: (from: ConversationState, to: ConversationState) => void;
  private isTransitioning = false;

  constructor(config: StateMachineConfig) {
    this.currentState = config.initialState;
    this.onStateChange = config.onStateChange;
    this.onInvalidTransition = config.onInvalidTransition;
    
    // Build transition map
    config.transitions.forEach(transition => {
      const key = transition.from;
      if (!this.transitions.has(key)) {
        this.transitions.set(key, []);
      }
      this.transitions.get(key)!.push(transition);
    });
  }

  /**
   * Get current state
   */
  getCurrentState(): ConversationState {
    return this.currentState;
  }

  /**
   * Check if a transition is valid
   */
  canTransitionTo(targetState: ConversationState): boolean {
    if (this.currentState === targetState) {
      return true; // Same state is always valid
    }

    const possibleTransitions = this.transitions.get(this.currentState) || [];
    const validTransition = possibleTransitions.find(
      transition => transition.to === targetState
    );

    if (!validTransition) {
      return false;
    }

    // Check condition if present
    if (validTransition.condition) {
      return validTransition.condition();
    }

    return true;
  }

  /**
   * Attempt to transition to a new state
   */
  async transitionTo(targetState: ConversationState): Promise<boolean> {
    if (this.isTransitioning) {
      console.warn(`🚫 [StateMachine] Already transitioning, ignoring transition to ${targetState}`);
      return false;
    }

    if (this.currentState === targetState) {
      console.log(`🔄 [StateMachine] Already in state ${targetState}`);
      return true;
    }

    if (!this.canTransitionTo(targetState)) {
      console.warn(`🚫 [StateMachine] Invalid transition: ${this.currentState} → ${targetState}`);
      if (this.onInvalidTransition) {
        this.onInvalidTransition(this.currentState, targetState);
      }
      return false;
    }

    const previousState = this.currentState;
    this.isTransitioning = true;

    try {
      // Find and execute the transition
      const possibleTransitions = this.transitions.get(this.currentState) || [];
      const transition = possibleTransitions.find(t => t.to === targetState);

      if (transition?.action) {
        console.log(`🔄 [StateMachine] Executing transition action: ${previousState} → ${targetState}`);
        await transition.action();
      }

      // Update state
      this.currentState = targetState;
      console.log(`✅ [StateMachine] State transition: ${previousState} → ${targetState}`);

      // Notify listeners
      if (this.onStateChange) {
        this.onStateChange(previousState, targetState);
      }

      return true;

    } catch (error) {
      console.error(`❌ [StateMachine] Error during transition ${previousState} → ${targetState}:`, error);
      return false;
    } finally {
      this.isTransitioning = false;
    }
  }

  /**
   * Get all possible next states from current state
   */
  getPossibleNextStates(): ConversationState[] {
    const possibleTransitions = this.transitions.get(this.currentState) || [];
    return possibleTransitions
      .filter(transition => !transition.condition || transition.condition())
      .map(transition => transition.to);
  }

  /**
   * Reset to initial state
   */
  reset(initialState?: ConversationState): void {
    const newState = initialState || 'idle';
    console.log(`🔄 [StateMachine] Resetting to state: ${newState}`);
    this.currentState = newState;
    this.isTransitioning = false;
  }
}

/**
 * Create a standard conversation state machine with common transitions
 */
export function createConversationStateMachine(
  onStateChange?: (from: ConversationState, to: ConversationState) => void,
  onInvalidTransition?: (from: ConversationState, to: ConversationState) => void
): ConversationStateMachine {
  const config: StateMachineConfig = {
    initialState: 'idle',
    onStateChange,
    onInvalidTransition,
    transitions: [
      // From idle
      {
        from: 'idle',
        to: 'listening',
        condition: () => true, // Can always start listening from idle
      },
      {
        from: 'idle',
        to: 'speaking',
        condition: () => true, // Can start speaking from idle (e.g., initial message)
      },

      // From listening
      {
        from: 'listening',
        to: 'idle',
        condition: () => true, // Can stop listening
      },
      {
        from: 'listening',
        to: 'processing',
        condition: () => true, // User finished speaking
      },
      {
        from: 'listening',
        to: 'speaking',
        condition: () => true, // Interrupt with AI response
      },

      // From processing
      {
        from: 'processing',
        to: 'speaking',
        condition: () => true, // AI response ready
      },
      {
        from: 'processing',
        to: 'idle',
        condition: () => true, // Processing failed or cancelled
      },
      {
        from: 'processing',
        to: 'listening',
        condition: () => true, // Continue listening (e.g., clarification needed)
      },

      // From speaking
      {
        from: 'speaking',
        to: 'idle',
        condition: () => true, // AI finished speaking
      },
      {
        from: 'speaking',
        to: 'listening',
        condition: () => true, // Direct transition to listening (auto-reactivation)
      },

      // Error recovery - from any state to idle
      {
        from: 'listening',
        to: 'idle',
        condition: () => true,
      },
      {
        from: 'processing',
        to: 'idle',
        condition: () => true,
      },
      {
        from: 'speaking',
        to: 'idle',
        condition: () => true,
      },
    ]
  };

  return new ConversationStateMachine(config);
}

/**
 * Singleton instance for global use
 */
export const conversationStateMachine = createConversationStateMachine(
  (from, to) => {
    console.log(`🔄 [GlobalStateMachine] State change: ${from} → ${to}`);
  },
  (from, to) => {
    console.warn(`🚫 [GlobalStateMachine] Invalid transition blocked: ${from} → ${to}`);
  }
);
