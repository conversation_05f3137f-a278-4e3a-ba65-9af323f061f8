import axios from "axios";
import type { IVoicesService } from "./impl/IVoicesService";

type VoiceConfig = {
  availableVoices: { name: string; default: boolean }[];
  defaultVoice: string;
};

const LANGUAGE = "es";
const API_KEY = import.meta.env.VITE_SPEECH_API_KEY;
const BASE_URL = import.meta.env.VITE_SPEECH_API_URL;

export class VoicesService implements IVoicesService {
  private static instance: VoicesService;
  private voiceId: string = "";
  private config?: VoiceConfig;
  private availableVoicesList: string[] = [];
  private isConfigured: boolean = false;

  private constructor() {}

  public static getInstance(): VoicesService {
    if (!VoicesService.instance) {
      VoicesService.instance = new VoicesService();
    }
    return VoicesService.instance;
  }

  private setVoiceId(voice: string) {
    this.voiceId = voice;
  }

  private async loadConfig(): Promise<VoiceConfig> {
    if (!this.config) {
      const res = await fetch("/azure-voice-conf.json");
      // console.log("Cargando configuración de voz desde:", res.url);
      if (!res.ok) {
        throw new Error(
          `Error cargando configuración de voz: HTTP ${res.status}`
        );
      }
      this.config = await res.json();
    }
    return this.config!;
  }

  public async getDefaultVoice(): Promise<string> {
    const { availableVoices, defaultVoice } = await this.loadConfig();
    this.availableVoicesList = availableVoices.map((v) => v.name);
    // console.log("Voces disponibles en configuración local:", this.availableVoicesList);
    // console.log("Voz por defecto configurada:", defaultVoice);
    return defaultVoice;
  }

  public async setVoiceByName(name: string): Promise<boolean> {
    // Primero verificar contra las voces configuradas localmente
    const { availableVoices } = await this.loadConfig();
    const localVoices = availableVoices.map((v) => v.name);

    if (localVoices.includes(name)) {
      this.setVoiceId(name);
      // console.log(`✅ Voz "${name}" configurada correctamente desde configuración local`);

      // Actualizar la lista de voces disponibles para referencia
      try {
        const remoteVoices = await this.getAvailableVoices();
        this.availableVoicesList = remoteVoices;

        if (!remoteVoices.includes(name)) {
          console.warn(`⚠️ La voz "${name}" está configurada localmente pero no está disponible en el servidor remoto`);
        }
      } catch (error) {
        console.warn(`⚠️ No se pudo verificar voces remotas, usando configuración local:`, error);
        this.availableVoicesList = localVoices;
      }

      return true;
    }

    console.warn(`⚠️ La voz "${name}" no está disponible en la configuración local.`);
    console.log(`Voces disponibles localmente:`, localVoices);
    return false;
  }

  public async configVoice(): Promise<boolean> {
    try {
      const voice = await this.getDefaultVoice();
      // console.log("Voz por defecto:", voice);
      const success = await this.setVoiceByName(voice);
      this.isConfigured = success;
      return success;
    } catch (error) {
      console.error("❌ [AzureVoicesService] Error configurando voz", error);
      this.availableVoicesList = [];
      this.isConfigured = false;
      return false;
    }
  }

  public isVoiceConfigured(): boolean {
    return this.isConfigured;
  }

  /**
   * Deshabilitar el servicio de voces
   */
  public disableVoice(): void {
    this.isConfigured = false;
    console.log("🔇 Servicio de voces deshabilitado");
  }

  /**
   * Habilitar el servicio de voces (requiere reconfiguración)
   */
  public async enableVoice(): Promise<boolean> {
    return await this.configVoice();
  }

  private async handleRequest<T>(request: Promise<any>): Promise<T> {
    try {
      const res = await request;
      if (import.meta.env.MODE === "development") {
        // console.log(`🟢 ${res.status} ${res.config.url}`, res.data);
      }
      return res.data as T;
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        if (error instanceof Error) {
          console.error(`🔴 RESPONSE ERROR: ${error.message}`);
        } else {
          console.error("🔴 RESPONSE ERROR: Unknown error", error);
        }
      }
      throw new Error("Error al realizar la solicitud");
    }
  }

  private getHeaders(): Record<string, string> {
    return {
      Authorization: `Bearer ${API_KEY}`,
    };
  }

  getAvailableVoices(): Promise<any> {
    const data = {
      language: LANGUAGE,
    };
    return this.handleRequest<string[]>(
      axios.post(`${BASE_URL}available_voices`, data, {
        headers: this.getHeaders(),
      })
    );
  }

  getAudio(text: string): Promise<any> {
    return this.handleRequest<Blob>(
      axios.post(
        `${BASE_URL}t2s`,
        {
          input_text: text,
          voice_params: { voice_id: this.voiceId, rate: 1.1, pitch: 0.5 },
          output_format: "mp3",
        },
        {
          responseType: "blob",
          headers: this.getHeaders(),
        }
      )
    );
  }
}
