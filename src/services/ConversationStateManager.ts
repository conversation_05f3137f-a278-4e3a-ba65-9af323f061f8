/**
 * Conversation State Manager
 * Eliminates nested callbacks and race conditions in conversation flow
 */

import type { ConversationState } from './impl/ISpeechRecognitionService';

export interface ConversationStateData {
  state: ConversationState;
  isActive: boolean;
  isListening: boolean;
  isAudioPlaying: boolean;
  canAutoActivate: boolean;
}

export type ConversationStateListener = (data: ConversationStateData) => void;

export interface ConversationStateManagerConfig {
  maxRetries: number;
  retryDelay: number;
  activationDelay: number;
  microphoneDelay: number;
}

const DEFAULT_CONFIG: ConversationStateManagerConfig = {
  maxRetries: 5,
  retryDelay: 500,
  activationDelay: 1000,
  microphoneDelay: 2500
};

class ConversationStateManager {
  private state: ConversationState = 'idle';
  private isActive = false;
  private isListening = false;
  private isAudioPlaying = false;
  private canAutoActivate = false;
  private listeners: Set<ConversationStateListener> = new Set();
  private config: ConversationStateManagerConfig;

  // Pending operations
  private pendingActivation: Promise<boolean> | null = null;
  private activationAbortController: AbortController | null = null;

  constructor(config: Partial<ConversationStateManagerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Get current state data
   */
  getState(): ConversationStateData {
    return {
      state: this.state,
      isActive: this.isActive,
      isListening: this.isListening,
      isAudioPlaying: this.isAudioPlaying,
      canAutoActivate: this.canAutoActivate
    };
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: ConversationStateListener): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(): void {
    const stateData = this.getState();
    this.listeners.forEach(listener => listener(stateData));
  }

  /**
   * Update conversation state
   */
  setConversationState(newState: ConversationState): void {
    if (this.state !== newState) {
      const previousState = this.state;
      console.log(`🔄 [ConversationManager] State: ${previousState} → ${newState}`);
      this.state = newState;
      this.notifyListeners();
      
      // Handle state transitions
      this.handleStateTransition(previousState, newState);
    }
  }

  /**
   * Update active status
   */
  setIsActive(active: boolean): void {
    if (this.isActive !== active) {
      console.log(`🔄 [ConversationManager] Active: ${this.isActive} → ${active}`);
      this.isActive = active;
      this.notifyListeners();
    }
  }

  /**
   * Update listening status
   */
  setIsListening(listening: boolean): void {
    if (this.isListening !== listening) {
      console.log(`🔄 [ConversationManager] Listening: ${this.isListening} → ${listening}`);
      this.isListening = listening;
      this.notifyListeners();
    }
  }

  /**
   * Update audio playing status
   */
  setIsAudioPlaying(playing: boolean): void {
    if (this.isAudioPlaying !== playing) {
      console.log(`🔄 [ConversationManager] Audio playing: ${this.isAudioPlaying} → ${playing}`);
      this.isAudioPlaying = playing;
      this.notifyListeners();
    }
  }

  /**
   * Set whether auto-activation is allowed
   */
  setCanAutoActivate(canActivate: boolean): void {
    if (this.canAutoActivate !== canActivate) {
      console.log(`🔄 [ConversationManager] Can auto-activate: ${this.canAutoActivate} → ${canActivate}`);
      this.canAutoActivate = canActivate;
      this.notifyListeners();
    }
  }

  /**
   * Handle state transitions with proper timing
   */
  private handleStateTransition(previousState: ConversationState, newState: ConversationState): void {
    // When audio finishes and we transition to idle
    if (previousState === 'speaking' && newState === 'idle') {
      this.handleAudioFinished();
    }
  }

  /**
   * Handle audio finished with clean promise-based approach
   */
  private handleAudioFinished(): void {
    console.log('🎤 [ConversationManager] Audio finished, handling transition...');

    // If we can auto-activate and conversation is not active
    if (this.canAutoActivate && !this.isActive) {
      this.scheduleAutoActivation();
    } else if (this.isActive) {
      this.scheduleMicrophoneReactivation();
    }
  }

  /**
   * Schedule auto-activation with proper state checking
   */
  private scheduleAutoActivation(): void {
    // Cancel any pending activation
    if (this.activationAbortController) {
      this.activationAbortController.abort();
    }

    this.activationAbortController = new AbortController();
    const signal = this.activationAbortController.signal;

    console.log('🎤 [ConversationManager] Scheduling auto-activation...');

    this.pendingActivation = this.waitForIdleAndActivate(signal);
  }

  /**
   * Wait for proper state synchronization and activate conversation
   */
  private async waitForIdleAndActivate(signal: AbortSignal): Promise<boolean> {
    const { maxRetries, retryDelay, activationDelay } = this.config;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      if (signal.aborted) {
        console.log('🚫 [ConversationManager] Auto-activation aborted');
        return false;
      }

      console.log(`🔍 [ConversationManager] Auto-activation attempt ${attempt + 1}/${maxRetries}`);

      // Check if states are synchronized
      if (this.isStateReadyForActivation()) {
        console.log('✅ [ConversationManager] States synchronized, activating...');
        
        try {
          // Wait a bit before activation
          await this.delay(activationDelay, signal);
          
          if (signal.aborted) return false;

          // Trigger activation (this should be provided by the caller)
          const success = await this.triggerConversationActivation();
          
          if (success) {
            console.log('✅ [ConversationManager] Auto-activation successful');
            this.canAutoActivate = false; // Prevent multiple activations
            return true;
          } else {
            console.warn('⚠️ [ConversationManager] Activation failed');
            return false;
          }
        } catch (error) {
          console.error('❌ [ConversationManager] Error during activation:', error);
          return false;
        }
      }

      // Wait before next attempt
      if (attempt < maxRetries - 1) {
        console.log(`⏳ [ConversationManager] States not ready, retrying in ${retryDelay}ms...`);
        await this.delay(retryDelay, signal);
      }
    }

    console.warn('⚠️ [ConversationManager] Auto-activation timeout - max retries reached');
    return false;
  }

  /**
   * Check if state is ready for activation
   */
  private isStateReadyForActivation(): boolean {
    const ready = this.state === 'idle' && 
                  !this.isAudioPlaying && 
                  !this.isListening;

    console.log('🔍 [ConversationManager] State check:', {
      state: this.state,
      isAudioPlaying: this.isAudioPlaying,
      isListening: this.isListening,
      ready
    });

    return ready;
  }

  /**
   * Schedule microphone reactivation for active conversations
   */
  private scheduleMicrophoneReactivation(): void {
    console.log('🎤 [ConversationManager] Scheduling microphone reactivation...');
    
    setTimeout(() => {
      if (this.isActive && this.state === 'idle' && !this.isListening && !this.isAudioPlaying) {
        console.log('🎤 [ConversationManager] Reactivating microphone...');
        this.triggerMicrophoneActivation();
      }
    }, this.config.microphoneDelay);
  }

  /**
   * Utility function for delays with abort signal
   */
  private delay(ms: number, signal?: AbortSignal): Promise<void> {
    return new Promise((resolve, reject) => {
      if (signal?.aborted) {
        reject(new Error('Aborted'));
        return;
      }

      const timeout = setTimeout(resolve, ms);
      
      signal?.addEventListener('abort', () => {
        clearTimeout(timeout);
        reject(new Error('Aborted'));
      });
    });
  }

  /**
   * These methods should be implemented by the consumer
   */
  private async triggerConversationActivation(): Promise<boolean> {
    // This should be injected or overridden
    console.warn('⚠️ [ConversationManager] triggerConversationActivation not implemented');
    return false;
  }

  private triggerMicrophoneActivation(): void {
    // This should be injected or overridden
    console.warn('⚠️ [ConversationManager] triggerMicrophoneActivation not implemented');
  }

  /**
   * Set activation handlers
   */
  setActivationHandlers(
    conversationActivator: () => Promise<boolean>,
    microphoneActivator: () => void
  ): void {
    this.triggerConversationActivation = conversationActivator;
    this.triggerMicrophoneActivation = microphoneActivator;
  }

  /**
   * Cancel any pending operations
   */
  cancelPendingOperations(): void {
    if (this.activationAbortController) {
      this.activationAbortController.abort();
      this.activationAbortController = null;
    }
    this.pendingActivation = null;
  }

  /**
   * Reset state
   */
  reset(): void {
    this.cancelPendingOperations();
    this.state = 'idle';
    this.isActive = false;
    this.isListening = false;
    this.isAudioPlaying = false;
    this.canAutoActivate = false;
    this.notifyListeners();
  }

  /**
   * Cleanup
   */
  destroy(): void {
    this.cancelPendingOperations();
    this.listeners.clear();
  }
}

// Singleton instance
export const conversationStateManager = new ConversationStateManager();
