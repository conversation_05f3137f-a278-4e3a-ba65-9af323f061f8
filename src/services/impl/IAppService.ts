import type { GenerateResponse } from "../../types/GenerateResponse";
import type { SelectedPresetResponse } from "../../types/SelectedPresetResponse";

// Define el contrato para el servicio principal de la aplicación.
export interface IAppService {
  // Establece el ID de sesión (sesid).
  setSesid(id: string): void;

  // Cambia la configuración predefinida (preset) que se usará en las llamadas a la API.
  // Puede ser 'gencharbot' o 'ia_vs_player'.
  setPreset(preset: "gencharbot" | "ia_vs_player"): Promise<void>;

  // Envía un texto (query) a la API para obtener una respuesta generada.
  // Opcionalmente puede recibir un ID para la consulta.
  generate(text: string, id?: string): Promise<GenerateResponse>;

  // Llama a la API para seleccionar el preset actual y obtener un ID de sesión.
  selectPreset(): Promise<SelectedPresetResponse>;

  // Registra una función (callback) que se ejecutará cuando se genere una URL de audio.
  setAudioCallback(callback: (audioUrl: string) => void): void;

  // Registra una función (callback) que se ejecutará cuando la reproducción del audio haya finalizado.
  setAudioFinishedCallback(callback: () => void): void;
}
