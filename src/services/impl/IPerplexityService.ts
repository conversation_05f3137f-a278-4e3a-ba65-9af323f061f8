import type {
  PerplexityGenerateResponse,
  PerplexityResetResponse,
  PerplexitySelectedPresetResponse,
  PerplexityPresetResponse
} from '../../types/PerplexityTypes';

export interface IPerplexityService {
  setSesid(id: string): void;
  setPresetId(id: string): void;
  generate(text: string, id?: string): Promise<PerplexityGenerateResponse>;
  reset(): Promise<PerplexityResetResponse>;
  selectPreset(): Promise<PerplexitySelectedPresetResponse>;
  getPresets(): Promise<PerplexityPresetResponse>;
  getCharacterInfo(characterName: string): Promise<PerplexityGenerateResponse>;
}
