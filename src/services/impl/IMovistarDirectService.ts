import type { MovieResult } from '../MovistarDirectService';

/**
 * Interfaz para el servicio directo de Movistar+ OTT
 */
export interface IMovistarDirectService {
  /**
   * Buscar películas por actor
   * @param actor Nombre del actor a buscar
   * @returns Promise con los resultados de la búsqueda formateados
   */
  searchByActor(actor: string): Promise<string>;

  /**
   * Buscar películas por título
   * @param title Título de la película a buscar
   * @returns Promise con los resultados de la búsqueda formateados
   */
  searchByTitle(title: string): Promise<string>;

  /**
   * Obtener la URL base configurada
   * @returns URL base del servicio
   */
  getBaseUrl(): string;

  /**
   * Verificar si el servicio está configurado correctamente
   * @returns true si la configuración es válida
   */
  isConfigured(): boolean;
}
