export type ConversationState = 'idle' | 'listening' | 'processing' | 'speaking';

export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
}

export interface ConversationStateChange {
  state: ConversationState;
  message?: string;
}

export interface ISpeechRecognitionService {
  // Control del reconocimiento de voz
  startListening(): Promise<boolean>;
  stopListening(): void;
  isListening(): boolean;
  
  // Control del estado de conversación
  getConversationState(): ConversationState;
  setConversationState(state: ConversationState): void;
  
  // Callbacks
  onResult(callback: (result: SpeechRecognitionResult) => void): void;
  onStateChange(callback: (change: ConversationStateChange) => void): void;
  onError(callback: (error: string) => void): void;
  
  // Control de micrófono inteligente
  enableSmartMicrophoneControl(): void;
  disableSmartMicrophoneControl(): void;
  
  // Configuración
  setLanguage(language: string): void;
  setContinuous(continuous: boolean): void;
  setInterimResults(interimResults: boolean): void;
  
  // Verificación de soporte
  isSupported(): boolean;
}
