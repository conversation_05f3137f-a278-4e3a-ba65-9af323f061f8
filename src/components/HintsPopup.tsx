import React from "react";
import type { GameHint } from "../services/ConversationStorage";

interface HintsPopupProps {
  hints: GameHint[];
  isOpen: boolean;
  onClose: () => void;
}

export const HintsPopup: React.FC<HintsPopupProps> = ({
  hints,
  isOpen,
  onClose,
}) => {
  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* Overlay */}
      <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          zIndex: 1000,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "20px",
        }}
        onClick={onClose}
      >
        {/* Modal */}
        <div
          style={{
            backgroundColor: "white",
            borderRadius: "12px",
            padding: "24px",
            maxWidth: "500px",
            width: "100%",
            maxHeight: "80vh",
            overflowY: "auto",
            boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
            position: "relative",
            animation: "slideIn 0.3s ease-out",
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "20px",
              paddingBottom: "12px",
              borderBottom: "2px solid #e9ecef",
            }}
          >
            <h3
              style={{
                margin: 0,
                color: "#495057",
                fontSize: "20px",
                fontWeight: "bold",
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              <span>💡</span>
              Pistas Recolectadas
            </h3>
            <button
              onClick={onClose}
              style={{
                background: "none",
                border: "none",
                fontSize: "24px",
                cursor: "pointer",
                color: "#6c757d",
                padding: "4px",
                borderRadius: "4px",
                transition: "background-color 0.2s ease",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#f8f9fa";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
              }}
            >
              ×
            </button>
          </div>

          {/* Content */}
          {hints.length === 0 ? (
            <div
              style={{
                textAlign: "center",
                padding: "40px 20px",
                color: "#6c757d",
              }}
            >
              <div style={{ fontSize: "48px", marginBottom: "16px" }}>🤔</div>
              <p style={{ margin: 0, fontSize: "16px" }}>
                Aún no tienes pistas.
              </p>
              <p style={{ margin: "8px 0 0 0", fontSize: "14px", opacity: 0.8 }}>
                Haz preguntas válidas para obtener pistas sobre el personaje.
              </p>
            </div>
          ) : (
            <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
              {hints.map((hint, index) => (
                <div
                  key={hint.id}
                  style={{
                    backgroundColor: "#f8f9fa",
                    border: "1px solid #e9ecef",
                    borderRadius: "8px",
                    padding: "16px",
                    position: "relative",
                    transition: "transform 0.2s ease, box-shadow 0.2s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = "translateY(-2px)";
                    e.currentTarget.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.1)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "translateY(0)";
                    e.currentTarget.style.boxShadow = "none";
                  }}
                >
                  {/* Número de pista */}
                  <div
                    style={{
                      position: "absolute",
                      top: "-8px",
                      left: "12px",
                      backgroundColor: "#007bff",
                      color: "white",
                      borderRadius: "12px",
                      padding: "4px 8px",
                      fontSize: "12px",
                      fontWeight: "bold",
                    }}
                  >
                    #{index + 1}
                  </div>

                  {/* Contenido de la pista */}
                  <div style={{ marginTop: "8px" }}>
                    <div
                      style={{
                        fontSize: "16px",
                        fontWeight: "500",
                        color: "#495057",
                        marginBottom: "8px",
                      }}
                    >
                      {hint.content}
                    </div>
                    <div
                      style={{
                        fontSize: "12px",
                        color: "#6c757d",
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <span>Pregunta #{hint.questionNumber}</span>
                      <span>
                        {new Date(hint.createdAt).toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Footer */}
          {hints.length > 0 && (
            <div
              style={{
                marginTop: "20px",
                paddingTop: "16px",
                borderTop: "1px solid #e9ecef",
                textAlign: "center",
                color: "#6c757d",
                fontSize: "14px",
              }}
            >
              Total: {hints.length} pista{hints.length !== 1 ? "s" : ""} recolectada{hints.length !== 1 ? "s" : ""}
            </div>
          )}
        </div>
      </div>

      {/* Animations */}
      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
          }
          to {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
      `}</style>
    </>
  );
};
