.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: transparent;
  position: relative;
  width: 100%;
  box-sizing: border-box;

  .header-left {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
  }

  .header-title {
    display: flex;
    font-weight: 600;
    font-style: SemiBold;
    font-size: 1.5rem;
    color: #88ffd5;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: -1;
    font-family: Playfair Display;

    @media (max-width: 768px) {
      display: none;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 2rem;

    @media (max-width: 768px) {
      gap: 1rem;
    }
  }
}