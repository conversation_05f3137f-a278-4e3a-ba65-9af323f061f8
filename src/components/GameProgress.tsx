import React from "react";
import type { GameProgress as GameProgressType } from "../services/ConversationStorage";

interface GameProgressProps {
  gameProgress: GameProgressType | null;
  onShowHints: () => void;
}

export const GameProgress: React.FC<GameProgressProps> = ({
  gameProgress,
  onShowHints,
}) => {
  if (!gameProgress) {
    return null;
  }

  const { questionsAsked, questionsRemaining, hints, gameFinished, gameWon } = gameProgress;

  return (
    <div
      style={{
        backgroundColor: "#f8f9fa",
        border: "1px solid #dee2e6",
        borderRadius: "8px",
        padding: "16px",
        marginBottom: "20px",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        flexWrap: "wrap",
        gap: "12px",
      }}
    >
      {/* Contador de preguntas */}
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <span style={{ fontSize: "18px" }}>❓</span>
        <div>
          <div style={{ fontSize: "14px", fontWeight: "bold", color: "#495057" }}>
            Preguntas: {questionsAsked}/20
          </div>
          <div style={{ fontSize: "12px", color: "#6c757d" }}>
            {questionsRemaining} restantes
          </div>
        </div>
      </div>

      {/* Barra de progreso */}
      <div style={{ flex: 1, minWidth: "120px", maxWidth: "200px" }}>
        <div
          style={{
            width: "100%",
            height: "8px",
            backgroundColor: "#e9ecef",
            borderRadius: "4px",
            overflow: "hidden",
          }}
        >
          <div
            style={{
              width: `${(questionsAsked / 20) * 100}%`,
              height: "100%",
              backgroundColor: gameFinished
                ? gameWon
                  ? "#28a745"
                  : "#dc3545"
                : "#007bff",
              transition: "width 0.3s ease",
            }}
          />
        </div>
      </div>

      {/* Pistas */}
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <button
          onClick={onShowHints}
          style={{
            display: "flex",
            alignItems: "center",
            gap: "6px",
            padding: "8px 12px",
            backgroundColor: hints.length > 0 ? "#17a2b8" : "#6c757d",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "13px",
            fontWeight: "500",
            transition: "background-color 0.2s ease",
          }}
          onMouseEnter={(e) => {
            if (hints.length > 0) {
              e.currentTarget.style.backgroundColor = "#138496";
            }
          }}
          onMouseLeave={(e) => {
            if (hints.length > 0) {
              e.currentTarget.style.backgroundColor = "#17a2b8";
            }
          }}
        >
          <span>💡</span>
          <span>Pistas ({hints.length})</span>
        </button>
      </div>

      {/* Estado del juego */}
      {gameFinished && (
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "6px",
            padding: "6px 12px",
            backgroundColor: gameWon ? "#d4edda" : "#f8d7da",
            color: gameWon ? "#155724" : "#721c24",
            borderRadius: "6px",
            fontSize: "13px",
            fontWeight: "bold",
          }}
        >
          <span>{gameWon ? "🎉" : "😔"}</span>
          <span>{gameWon ? "¡Ganaste!" : "Juego terminado"}</span>
        </div>
      )}
    </div>
  );
};
