.debug-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.9);
  color: #00ff00;
  border: 1px solid #333;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  z-index: 9999;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);

  &.collapsed {
    width: auto;
  }

  &.expanded {
    width: 400px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .debug-header {
    padding: 8px 12px;
    background: rgba(0, 255, 0, 0.1);
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    user-select: none;

    &:hover {
      background: rgba(0, 255, 0, 0.2);
    }

    .debug-controls {
      display: flex;
      align-items: center;
      gap: 8px;

      .refresh-btn, .export-btn {
        background: none;
        border: 1px solid #555;
        color: #00ff00;
        padding: 2px 6px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 10px;
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }

      .refresh-btn {
        &.active {
          background: rgba(0, 255, 0, 0.2);
          border-color: #00ff00;
        }

        &.inactive {
          background: rgba(255, 0, 0, 0.2);
          border-color: #ff6666;
          color: #ff6666;
        }
      }

      .export-btn {
        background: rgba(0, 170, 255, 0.2);
        border-color: #00aaff;
        color: #00aaff;

        &:hover {
          background: rgba(0, 170, 255, 0.3);
        }
      }
    }

    .toggle-icon {
      font-size: 10px;
      transition: transform 0.2s ease;
    }
  }

  .debug-content {
    padding: 12px;
    max-height: calc(80vh - 40px);
    overflow-y: auto;

    /* Custom scrollbar */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 255, 0, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(0, 255, 0, 0.5);
      }
    }
  }

  .debug-section {
    margin-bottom: 16px;
    border: 1px solid #444;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.3);

    h4 {
      margin: 0;
      padding: 6px 8px;
      background: rgba(0, 255, 0, 0.1);
      border-bottom: 1px solid #444;
      font-size: 11px;
      font-weight: bold;
      color: #00ff00;
    }

    .debug-item {
      display: flex;
      padding: 4px 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      &:last-child {
        border-bottom: none;
      }

      .label {
        flex: 0 0 140px;
        color: #ffff00;
        font-weight: bold;
        margin-right: 8px;
        font-size: 10px;
      }

      .value {
        flex: 1;
        color: #00ffff;
        word-break: break-all;
        font-size: 10px;
        line-height: 1.3;

        &:empty::after {
          content: "—";
          color: #666;
        }
      }
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    top: 5px;
    right: 5px;
    max-width: calc(100vw - 20px);

    &.expanded {
      width: calc(100vw - 20px);
      max-height: 70vh;
    }

    .debug-content {
      padding: 8px;
    }

    .debug-section {
      .debug-item {
        flex-direction: column;

        .label {
          flex: none;
          margin-bottom: 2px;
        }

        .value {
          margin-left: 8px;
        }
      }
    }
  }

  /* Animation for expand/collapse */
  .debug-content {
    transition: all 0.3s ease;
  }

  /* Color coding for different states */
  .debug-item {
    .value {
      &[data-type="true"] {
        color: #00ff00;
      }

      &[data-type="false"] {
        color: #ff6666;
      }

      &[data-type="null"], &[data-type="undefined"] {
        color: #888;
        font-style: italic;
      }

      &[data-type="error"] {
        color: #ff4444;
        font-weight: bold;
      }
    }
  }

  /* Special styling for important states */
  .debug-section {
    &[data-section="game"] {
      border-color: #00ff00;

      h4 {
        background: rgba(0, 255, 0, 0.2);
      }
    }

    &[data-section="audio"] {
      border-color: #ffaa00;

      h4 {
        background: rgba(255, 170, 0, 0.2);
        color: #ffaa00;
      }
    }

    &[data-section="conversation"] {
      border-color: #00aaff;

      h4 {
        background: rgba(0, 170, 255, 0.2);
        color: #00aaff;
      }
    }
  }

  /* Highlight active/important values */
  .debug-item {
    &.highlight {
      background: rgba(255, 255, 0, 0.1);

      .value {
        color: #ffff00;
        font-weight: bold;
      }
    }

    &.error {
      background: rgba(255, 0, 0, 0.1);

      .value {
        color: #ff4444;
      }
    }
  }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .debug-panel {
    background: rgba(20, 20, 20, 0.95);
    border-color: #555;
  }
}

/* Print styles - hide debug panel when printing */
@media print {
  .debug-panel {
    display: none !important;
  }
}
