@use "./../../../animations.scss";

// Main
.view-main-menu {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  align-items: flex-start;
  padding: 0 200px;
  overflow: auto;

  @media (max-width: 768px) {
    padding: 0 2rem;
  }

  .container {
    display: flex;
    align-items: center;
    flex-direction: column;
    width: 100%;

    &-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      padding: 2rem 0;

      .header-title {
        font-family: Playfair Display;
        font-style: SemiBold;
        font-size: 4rem;
        text-align: center;
        width: 100%;
        font-weight: 600;
      }

      .header-subtitle {
        width: 100%;
        text-align: center;
      }
    }

    &-modes {
      display: flex;
      flex-direction: row;
      gap: 2rem;
      padding-bottom: 2rem;

      .modes-card {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 2rem;
        width: 100%;
        flex-wrap: wrap;
      }

      .image {
        &-enygma {
          border-radius: 160px;
          border: 2px solid #88ffd5;
          box-shadow: 0px 0px 12px 0px #88ffd5;
        }
      }

      .modes-description {
        text-align: center;
      }
    }
  }
}

.rules-button {
  padding: 2rem;
  width: 200px;
  position: absolute;
  left: 5px;
  bottom: 5px;
  z-index: 2;
  transition: all 0.2s;
  cursor: pointer;
  &:hover {
    scale: 1.02;
  }
  .book-image {
    animation: vibrateRotate 1s ease-in-out infinite;
  }
}
