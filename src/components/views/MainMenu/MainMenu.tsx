// React core
import { useEffect, useState } from "react";
// Third-party library imports
import { Image, PrimaryButton } from "microapps";
// Services
// Components
// Utils & Constants & Helpers
// Styles
import "./MainMenu.scss";

interface GameMode {
  id: string;
  enabled: boolean;
  image: string;
  mode: "ia_vs_player";
  buttonText: string;
  description: string;
}

interface MainMenuProps {
  onStartGame: () => void;
  onShowRules: () => void;
  aiLoading: boolean;
}

export const MainMenu: React.FC<MainMenuProps> = ({
  onStartGame,
  onShowRules,
  aiLoading,
}) => {
  const [gameModes, setGameModes] = useState<GameMode[]>([]);

  useEffect(() => {
    const loadGameModes = async () => {
      try {
        const response = await fetch("/game-modes.json");
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();
        setGameModes(data.gameModes || []);
      } catch (error) {
        console.error("Error loading game modes:", error);
      }
    };

    loadGameModes();
  }, []);

  return (
    <div className="view-main-menu">
      <div className="container">
        <div className="container-header">
          <h1 className="header-title">Enygma</h1>
          <p className="header-subtitle body1">
            ¿Puedes adivinar el personaje que está pensando Enygma?
          </p>
        </div>

        <div className="container-modes">
          {gameModes
            .filter((mode) => mode.enabled)
            .map((mode) => (
              <div key={mode.id} className="modes-card">
                <Image
                  src={mode.image}
                  alt="Enygma"
                  className="image-enygma"
                  width="250px"
                  aspectRatio="7:10"
                />

                <PrimaryButton
                  onClick={onStartGame}
                  isDisabled={aiLoading}
                  isLoading={aiLoading}
                  text={aiLoading ? "Iniciando Juego..." : mode.buttonText}
                  textColor="#001428"
                  spinnerColor="#000"
                  className="primary-button start-button"
                />
              </div>
            ))}
        </div>
      </div>

      <button onClick={onShowRules} className="rules-button">
        <Image
          width="100%"
          aspectRatio="1:1"
          src="assets/game/book.png"
          alt="Book"
          className="book-image"
        />
        <p className="rules-text bold body1">Reglas</p>
      </button>
    </div>
  );
};
