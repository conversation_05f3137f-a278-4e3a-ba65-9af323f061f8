.game-exit {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  .game-exit-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    max-width: 700px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;

    @media (max-width: 768px) {
      padding: 24px;
      margin: 10px;
      max-height: 95vh;
    }
  }

  .exit-header {
    text-align: center;
    margin-bottom: 32px;

    .exit-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .exit-title {
      color: #333;
      margin: 0 0 8px 0;
      font-size: 32px;
      font-weight: 600;

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }

    .exit-subtitle {
      color: #666;
      margin: 0;
      font-size: 16px;
      line-height: 1.5;
    }
  }

  .exit-warning {
    margin-bottom: 32px;

    .warning-card {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      gap: 16px;
      align-items: flex-start;

      .warning-icon {
        font-size: 24px;
        flex-shrink: 0;
      }

      .warning-content {
        flex: 1;

        h3 {
          color: #856404;
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 600;
        }

        p {
          color: #856404;
          margin: 0;
          line-height: 1.5;
          font-size: 14px;
        }
      }

      @media (max-width: 768px) {
        padding: 16px;
        gap: 12px;
      }
    }
  }

  .exit-options {
    margin-bottom: 32px;

    .option-card {
      h3 {
        color: #333;
        margin: 0 0 20px 0;
        font-size: 20px;
        font-weight: 600;
        text-align: center;
      }

      .options-grid {
        display: grid;
        gap: 16px;

        @media (min-width: 768px) {
          grid-template-columns: 1fr 1fr;
        }

        .option-button {
          background: white;
          border: 2px solid #e9ecef;
          border-radius: 12px;
          padding: 20px;
          cursor: pointer;
          transition: all 0.2s ease;
          text-align: center;
          display: flex;
          flex-direction: column;
          gap: 8px;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          &.continue {
            border-color: #28a745;

            &:hover {
              background: #f8fff9;
              border-color: #20c997;
            }

            .option-icon {
              color: #28a745;
            }

            .option-text {
              color: #28a745;
            }
          }

          &.exit {
            border-color: #dc3545;

            &:hover {
              background: #fff5f5;
              border-color: #c82333;
            }

            .option-icon {
              color: #dc3545;
            }

            .option-text {
              color: #dc3545;
            }
          }

          .option-icon {
            font-size: 32px;
          }

          .option-text {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
          }

          .option-description {
            font-size: 12px;
            color: #666;
            margin: 0;
            line-height: 1.4;
          }

          @media (max-width: 768px) {
            padding: 16px;

            .option-icon {
              font-size: 28px;
            }

            .option-text {
              font-size: 14px;
            }

            .option-description {
              font-size: 11px;
            }
          }
        }
      }
    }
  }

  .exit-stats {
    .stats-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 12px;
      padding: 20px;

      h3 {
        color: #333;
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
      }

      .stats-grid {
        display: grid;
        gap: 12px;

        .stat-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #e9ecef;

          &:last-child {
            border-bottom: none;
          }

          .stat-label {
            color: #666;
            font-size: 14px;
          }

          .stat-value {
            color: #333;
            font-weight: 600;
            font-size: 14px;
          }
        }
      }

      @media (max-width: 768px) {
        padding: 16px;
      }
    }
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
