import React from "react";
import "./GameExit.scss";

interface GameExitProps {
  onConfirmExit?: () => void;
  onCancelExit?: () => void;
}

/**
 * Game Exit Component
 * 
 * Displays exit confirmation screen with options to save progress or quit.
 * Features:
 * - Exit confirmation dialog
 * - Option to save current progress
 * - Return to game option
 */
export const GameExit: React.FC<GameExitProps> = ({
  onConfirmExit,
  onCancelExit
}) => {
  const handleConfirmExit = () => {
    if (onConfirmExit) {
      onConfirmExit();
    }
  };

  const handleCancelExit = () => {
    if (onCancelExit) {
      onCancelExit();
    }
  };

  return (
    <div className="game-exit">
      <div className="game-exit-content">
        <div className="exit-header">
          <div className="exit-icon">🚪</div>
          <h1 className="exit-title">¿Salir del Juego?</h1>
          <p className="exit-subtitle">
            ¿Estás seguro de que quieres salir del juego actual?
          </p>
        </div>

        <div className="exit-warning">
          <div className="warning-card">
            <div className="warning-icon">⚠️</div>
            <div className="warning-content">
              <h3>¡Atención!</h3>
              <p>
                Si sales ahora, perderás todo el progreso del juego actual, 
                incluyendo las pistas descubiertas y el personaje generado.
              </p>
            </div>
          </div>
        </div>

        <div className="exit-options">
          <div className="option-card">
            <h3>¿Qué quieres hacer?</h3>
            <div className="options-grid">
              <button 
                className="option-button continue"
                onClick={handleCancelExit}
              >
                <span className="option-icon">🎮</span>
                <span className="option-text">Continuar Jugando</span>
                <span className="option-description">
                  Volver al juego y seguir adivinando
                </span>
              </button>

              <button 
                className="option-button exit"
                onClick={handleConfirmExit}
              >
                <span className="option-icon">🏠</span>
                <span className="option-text">Salir al Menú</span>
                <span className="option-description">
                  Terminar el juego y volver al menú principal
                </span>
              </button>
            </div>
          </div>
        </div>

        <div className="exit-stats">
          <div className="stats-card">
            <h3>Progreso Actual</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-label">Pistas descubiertas:</span>
                <span className="stat-value">3</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Vidas restantes:</span>
                <span className="stat-value">2/5</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Tiempo jugado:</span>
                <span className="stat-value">5 min</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
