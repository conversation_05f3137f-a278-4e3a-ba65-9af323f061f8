.game-live {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  .game-live-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    max-width: 600px;
    width: 100%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;
    text-align: center;

    @media (max-width: 768px) {
      padding: 24px;
      margin: 10px;
    }
  }

  .lives-header {
    margin-bottom: 32px;

    .lives-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .lives-title {
      color: #333;
      margin: 0;
      font-size: 32px;
      font-weight: 600;

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }
  }

  .lives-display {
    margin-bottom: 32px;

    .lives-counter {
      font-size: 48px;
      font-weight: bold;
      color: #333;

      .remaining {
        color: #ff6b6b;
      }

      .separator {
        color: #ccc;
        margin: 0 8px;
      }

      .total {
        color: #666;
      }

      @media (max-width: 768px) {
        font-size: 36px;
      }
    }
  }

  .lives-visual {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 32px;
    flex-wrap: wrap;

    .life-heart {
      font-size: 32px;
      transition: all 0.3s ease;
      animation: heartbeat 2s infinite;

      &.used {
        opacity: 0.5;
        animation: none;
      }

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }
  }

  .lives-info {
    display: grid;
    gap: 24px;
    text-align: left;

    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }

    .info-card,
    .tips-card {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 12px;
      border: 1px solid #e9ecef;

      h3 {
        color: #333;
        margin: 0 0 12px 0;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        color: #666;
        margin: 0;
        line-height: 1.6;

        strong {
          color: #333;
        }

        .encouragement {
          display: block;
          color: #28a745;
          font-weight: 500;
          margin-top: 8px;
        }

        .warning {
          display: block;
          color: #dc3545;
          font-weight: 500;
          margin-top: 8px;
        }
      }

      ul {
        color: #666;
        margin: 0;
        padding-left: 20px;
        line-height: 1.6;

        li {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      @media (max-width: 768px) {
        padding: 16px;
      }
    }
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
