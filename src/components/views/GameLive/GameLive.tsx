import React from "react";
import "./GameLive.scss";

/**
 * Game Live Component
 * 
 * Displays the current lives/attempts remaining in the game.
 * Features:
 * - Visual representation of remaining lives
 * - Game progress information
 * - Lives counter
 */
export const GameLive: React.FC = () => {
  // TODO: Get actual lives count from game state
  const totalLives = 5;
  const remainingLives = 3;
  const usedLives = totalLives - remainingLives;

  return (
    <div className="game-live">
      <div className="game-live-content">
        <div className="lives-header">
          <div className="lives-icon">❤️</div>
          <h1 className="lives-title">Vidas Restantes</h1>
        </div>

        <div className="lives-display">
          <div className="lives-counter">
            <span className="remaining">{remainingLives}</span>
            <span className="separator">/</span>
            <span className="total">{totalLives}</span>
          </div>
        </div>

        <div className="lives-visual">
          {Array.from({ length: totalLives }, (_, index) => (
            <div
              key={index}
              className={`life-heart ${index < remainingLives ? 'active' : 'used'}`}
            >
              {index < remainingLives ? '❤️' : '💔'}
            </div>
          ))}
        </div>

        <div className="lives-info">
          <div className="info-card">
            <h3>Estado del Juego</h3>
            <p>
              Has usado <strong>{usedLives}</strong> de tus <strong>{totalLives}</strong> vidas.
              {remainingLives > 0 ? (
                <span className="encouragement">
                  ¡Sigue adelante, puedes lograrlo! 💪
                </span>
              ) : (
                <span className="warning">
                  ¡Cuidado! No te quedan más vidas. 😰
                </span>
              )}
            </p>
          </div>

          <div className="tips-card">
            <h3>Consejos</h3>
            <ul>
              <li>Haz preguntas específicas para obtener mejores pistas</li>
              <li>Revisa las pistas anteriores antes de preguntar</li>
              <li>Piensa bien antes de dar tu respuesta final</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
