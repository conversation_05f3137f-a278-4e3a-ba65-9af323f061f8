.game-hint {
  min-height: 100vh;
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  .game-hint-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;

    @media (max-width: 768px) {
      padding: 24px;
      margin: 10px;
      max-height: 95vh;
    }
  }

  .hints-header {
    text-align: center;
    margin-bottom: 32px;

    .hints-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .hints-title {
      color: #333;
      margin: 0 0 8px 0;
      font-size: 32px;
      font-weight: 600;

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }

    .hints-subtitle {
      color: #666;
      margin: 0;
      font-size: 16px;

      strong {
        color: #333;
      }
    }
  }

  .no-hints {
    text-align: center;
    padding: 40px 20px;

    .no-hints-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }

    h3 {
      color: #333;
      margin: 0 0 12px 0;
      font-size: 24px;
    }

    p {
      color: #666;
      margin: 0;
      font-size: 16px;
      line-height: 1.6;
    }
  }

  .hints-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 32px;

    .hint-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      gap: 16px;
      transition: all 0.2s ease;

      &:hover {
        background: #e9ecef;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .hint-number {
        background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
        flex-shrink: 0;
      }

      .hint-content {
        flex: 1;

        .hint-question,
        .hint-answer {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .question-label,
        .answer-label {
          font-weight: 600;
          color: #333;
          display: block;
          margin-bottom: 4px;
          font-size: 14px;
        }

        .question-text {
          color: #007bff;
          font-style: italic;
          display: block;
          margin-bottom: 8px;
        }

        .answer-text {
          color: #666;
          display: block;
          line-height: 1.5;
        }

        .hint-timestamp {
          color: #999;
          font-size: 12px;
          margin-top: 8px;
          font-style: italic;
        }
      }

      @media (max-width: 768px) {
        padding: 16px;
        gap: 12px;

        .hint-number {
          width: 28px;
          height: 28px;
          font-size: 12px;
        }
      }
    }
  }

  .hints-summary {
    .summary-card {
      background: #e3f2fd;
      border: 1px solid #bbdefb;
      border-radius: 12px;
      padding: 20px;

      h3 {
        color: #1976d2;
        margin: 0 0 12px 0;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        color: #666;
        margin: 0;
        line-height: 1.6;
        font-size: 14px;
      }

      @media (max-width: 768px) {
        padding: 16px;
      }
    }
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Scrollbar styling
.game-hint-content {
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
