import React from "react";
import "./GameHint.scss";

/**
 * Game Hint Component
 * 
 * Displays all the hints/clues discovered during the game.
 * Features:
 * - List of all discovered hints
 * - Chronological order
 * - Visual representation of progress
 */
export const GameHint: React.FC = () => {
  // TODO: Get actual hints from game state
  const hints = [
    {
      id: 1,
      question: "¿Es una persona real?",
      answer: "Sí, es una persona que existió en la vida real.",
      timestamp: "Hace 5 minutos"
    },
    {
      id: 2,
      question: "¿Es del siglo XX?",
      answer: "No, vivió en una época anterior al siglo XX.",
      timestamp: "Hace 3 minutos"
    },
    {
      id: 3,
      question: "¿Era un científico?",
      answer: "Sí, era conocido por sus contribuciones a la ciencia.",
      timestamp: "Hace 1 minuto"
    }
  ];

  return (
    <div className="game-hint">
      <div className="game-hint-content">
        <div className="hints-header">
          <div className="hints-icon">💡</div>
          <h1 className="hints-title">Pistas <PERSON>bie<PERSON></h1>
          <p className="hints-subtitle">
            Has descubierto <strong>{hints.length}</strong> pistas hasta ahora
          </p>
        </div>

        {hints.length === 0 ? (
          <div className="no-hints">
            <div className="no-hints-icon">🤔</div>
            <h3>Aún no hay pistas</h3>
            <p>Haz preguntas para descubrir pistas sobre el personaje misterioso.</p>
          </div>
        ) : (
          <div className="hints-list">
            {hints.map((hint, index) => (
              <div key={hint.id} className="hint-card">
                <div className="hint-number">
                  {index + 1}
                </div>
                <div className="hint-content">
                  <div className="hint-question">
                    <span className="question-label">Pregunta:</span>
                    <span className="question-text">{hint.question}</span>
                  </div>
                  <div className="hint-answer">
                    <span className="answer-label">Respuesta:</span>
                    <span className="answer-text">{hint.answer}</span>
                  </div>
                  <div className="hint-timestamp">
                    {hint.timestamp}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="hints-summary">
          <div className="summary-card">
            <h3>Resumen de Pistas</h3>
            <p>
              Usa estas pistas para hacer preguntas más específicas y acercarte 
              a la respuesta correcta. Recuerda que cada pregunta cuenta, 
              ¡así que piensa bien antes de preguntar!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
