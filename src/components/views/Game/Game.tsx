// React core
// Third-party library imports
import { Image, Mic } from "microapps";
// Services
// Components
import { SimpleVoiceChat } from "../../SimpleVoiceChat";
// Utils & Constants & Helpers
// Styles
import "./Game.scss";
import { MicAdapter } from "../../MicAdapter";

interface GameProps {
  generatedCharacter: string;
  gameStarted: boolean;
  isGameStarted: boolean;
  initialMessage: string;
  onGameEnd: (gameWon: boolean) => void;
  onShowGameLive: () => void;
  onShowGameHint: () => void;
  onShowGameExit: () => void;
}

export const Game: React.FC<GameProps> = ({
  generatedCharacter,
  gameStarted,
  isGameStarted,
  initialMessage,
  onGameEnd,
  onShowGameLive,
  onShowGameHint,
  onShowGameExit,
}) => {
  return (
    <div className="view-game">
      <div className="container">
        <div className="menu-left">
          <div className="enygma-logo">
            <Image
              src="assets/game/enygma.png"
              alt="Enygma"
              className="enygma-image"
              width="180px"
              aspectRatio="1:1"
            />

            <div className="speaking">
  <MicAdapter
    conversationState={conversationState} // p.ej. "idle" | "listening" | "processing" | "speaking"
    isVoiceActive={isVoiceActive}
    micLevel={micLevel}
    onToggle={toggleVoice}
    voiceError={voiceError}
    id="game-mic"
    className="game-mic-wrapper"
  />
</div>
          </div>
        </div>

        <SimpleVoiceChat
          generatedCharacter={generatedCharacter}
          isGameStarted={isGameStarted}
          initialMessage={initialMessage}
          onGameEnd={onGameEnd}
        />

        {(generatedCharacter || gameStarted) && (
          <div className="menu-right">
            <div className="game-navigation">
              <div onClick={onShowGameLive} className="image-button">
                <Image
                  width="100%"
                  aspectRatio="1:1"
                  src="assets/game/lives.png"
                  alt="Vidas"
                  className="book-image"
                />
              </div>

              <div onClick={onShowGameHint} className="image-button">
                <Image
                  width="100%"
                  aspectRatio="1:1"
                  src="assets/game/clues.png"
                  alt="Pistas"
                  className="clues-image"
                />
                <p className="body2 bold">Pistas</p>
              </div>

              <div onClick={onShowGameExit} className="image-button">
                <Image
                  width="100%"
                  aspectRatio="1:1"
                  src="assets/game/exit.png"
                  alt="Salir"
                  className="exit-image"
                />
                <p className="body2 bold">Salir</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
