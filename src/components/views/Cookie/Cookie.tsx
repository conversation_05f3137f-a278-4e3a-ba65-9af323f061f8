// React core
// Third-party library imports
import { Modal } from "microapps";
// Services
// Components
// Utils & Constants & Helpers
import { startBackgroundMusic } from '../../../utils/audioUtils';
// Styles

interface CookieProps {
  onAccept: () => void;
  onDecline: () => void;
}

export const Cookie: React.FC<CookieProps> = ({ onAccept, onDecline }) => {
  /**
   * Handle accept button click - play background music and proceed
   * Catches and logs any errors during music playback
   * @returns void
   */
  const handleAcceptAndPlay = async () => {
    try {
      await startBackgroundMusic('/assets/sounds/sound.mp3');
    } catch (error) {
      console.warn('⚠️ Error reproduciendo música de fondo:', error);
    }

    onAccept();
  };

  /**
   * Handle decline button click - play background music and proceed
   * Catches and logs any errors during music playback
   * @returns void
   */
  const handleDeclineAndPlay = async () => {
    try {
      await startBackgroundMusic('/assets/sounds/sound.mp3');
    } catch (error) {
      console.warn('⚠️ Error reproduciendo música de fondo:', error);
    }

    onDecline();
  }

  return (
    <Modal
      title="Ayúdanos a mejorar"
      onClose={handleDeclineAndPlay}
      onCancel={handleDeclineAndPlay}
      onConfirm={handleAcceptAndPlay}
      cancelText="Cancelar"
      confirmText="Aceptar"
      body="Utilizamos cookies para ofrecerte un servicio más ágil y adaptado a tus preferencias."
    />
  );
};
