// React core
// Third-party library imports
import { PrimaryButton } from "microapps";
// Services
// Components
// Utils & Constants & Helpers
import { startBackgroundMusic } from '../../../utils/audioUtils';
// Styles
import "./Welcome.scss";

interface WelcomeProps {
  handleAcceptAndPlay: () => void;
  aiLoading: boolean;
}

export const Welcome: React.FC<WelcomeProps> = ({
  handleAcceptAndPlay,
  aiLoading,
}) => {
  /**
   * Handle start button click - play background music and proceed
   * Catches and logs any errors during music playback
   * @returns void
   */
  const handleStartAndPlay = async () => {
    try {
      await startBackgroundMusic('/assets/sounds/sound.mp3');
    } catch (error) {
      console.warn('⚠️ Error reproduciendo música de fondo:', error);
    }

    handleAcceptAndPlay();
  };

  return (
    <div className="view-welcome">
      <div className="container">
        <h1 className="welcome-title title1 bold">
          El velo del misterio se alza.
        </h1>

        <p className="welcome-body body1">
          ¿Estás listo para enfrentarte a Enygma y desvelar el personaje oculto
          en el que está pensando?
        </p>

        <PrimaryButton
          onClick={handleStartAndPlay}
          text="Empezar"
          backgroundColor="#88FFD5"
          textColor="#001428"
          borderRadius="8px"
          spinnerColor="#000"
          className="welcome-button primary-button"
          isDisabled={aiLoading}
        />
      </div>
    </div>
  );
};
