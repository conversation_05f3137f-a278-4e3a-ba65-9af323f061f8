@use './../../../animations.scss';

.view-welcome {
  outline: 1px solid red;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInUp 0.6s ease-out forwards;

  .container {
    max-width: 600px;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 2rem;

    .welcome-title {
      color: #88ffd5;
      text-shadow: 0 0 20px rgba(136, 255, 213, 0.5);
      text-align: center;
      font-family: Playfair Display;
      font-style: SemiBold;
      line-height: 100%;
    }

    .welcome-body {
      color: #e0e0e0;
      max-width: 500px;
      text-align: center;
    }

    .welcome-disclaimer {
      color: #94a3b8;
      opacity: 0;
      text-align: center;
      &.isInitializing {
        opacity: 1;
      }
    }

    button {
      margin: 0 auto;
    }
  }
}
