import React from "react";

interface GameEndScreenProps {
  gameWon: boolean;
  onClose: () => void;
  onNewGame: () => void;
}

export const GameEndScreen: React.FC<GameEndScreenProps> = ({
  gameWon,
  onClose,
  onNewGame,
}) => {
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
        padding: "20px",
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "16px",
          padding: "32px",
          maxWidth: "600px",
          width: "100%",
          maxHeight: "80vh",
          overflowY: "auto",
          boxShadow: "0 20px 40px rgba(0, 0, 0, 0.3)",
          position: "relative",
        }}
      >
        {/* Header */}
        <div style={{ textAlign: "center", marginBottom: "24px" }}>
          <div style={{ fontSize: "48px", marginBottom: "16px" }}>
            {gameWon ? "🎉" : "😔"}
          </div>
          <h2
            style={{
              fontSize: "28px",
              fontWeight: "bold",
              color: gameWon ? "#28a745" : "#dc3545",
              margin: "0 0 8px 0",
            }}
          >
            {gameWon ? "¡Felicitaciones!" : "Juego Terminado"}
          </h2>
          <p
            style={{
              fontSize: "16px",
              color: "#6c757d",
              margin: 0,
            }}
          >
            {gameWon
              ? "¡Has adivinado correctamente el personaje!"
              : "No lograste adivinar el personaje esta vez."}
          </p>
        </div>

        {/* Perplexity Section */}
        <div
          style={{
            backgroundColor: "#f8f9fa",
            borderRadius: "12px",
            padding: "20px",
            marginBottom: "20px",
            border: "1px solid #dee2e6",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "12px",
              marginBottom: "16px",
            }}
          >
            <div
              style={{
                width: "40px",
                height: "40px",
                backgroundColor: "#6366f1",
                borderRadius: "8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "20px",
              }}
            >
              🧠
            </div>
            <div>
              <h3
                style={{
                  fontSize: "18px",
                  fontWeight: "bold",
                  color: "#495057",
                  margin: 0,
                }}
              >
                Perplexity AI
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: "#6c757d",
                  margin: 0,
                }}
              >
                Tu asistente de IA conversacional
              </p>
            </div>
          </div>
          <p
            style={{
              fontSize: "14px",
              color: "#495057",
              lineHeight: "1.5",
              margin: 0,
            }}
          >
            Perplexity es una plataforma de IA que te permite hacer preguntas y
            obtener respuestas precisas con fuentes verificadas. Ideal para
            investigación, aprendizaje y resolución de dudas complejas.
          </p>
        </div>

        {/* Movistar+ Section */}
        <div
          style={{
            backgroundColor: "#f8f9fa",
            borderRadius: "12px",
            padding: "20px",
            marginBottom: "24px",
            border: "1px solid #dee2e6",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "12px",
              marginBottom: "16px",
            }}
          >
            <div
              style={{
                width: "40px",
                height: "40px",
                backgroundColor: "#0066cc",
                borderRadius: "8px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "20px",
              }}
            >
              📺
            </div>
            <div>
              <h3
                style={{
                  fontSize: "18px",
                  fontWeight: "bold",
                  color: "#495057",
                  margin: 0,
                }}
              >
                Movistar+
              </h3>
              <p
                style={{
                  fontSize: "14px",
                  color: "#6c757d",
                  margin: 0,
                }}
              >
                Entretenimiento sin límites
              </p>
            </div>
          </div>
          <p
            style={{
              fontSize: "14px",
              color: "#495057",
              lineHeight: "1.5",
              margin: 0,
            }}
          >
            Disfruta de miles de películas, series, documentales y contenido
            exclusivo. Con Movistar+ tienes acceso a lo mejor del entretenimiento
            nacional e internacional, disponible cuando y donde quieras.
          </p>
        </div>

        {/* Action Buttons */}
        <div
          style={{
            display: "flex",
            gap: "12px",
            justifyContent: "center",
            flexWrap: "wrap",
          }}
        >
          <button
            onClick={onNewGame}
            style={{
              backgroundColor: "#007bff",
              color: "white",
              border: "none",
              borderRadius: "8px",
              padding: "12px 24px",
              fontSize: "16px",
              fontWeight: "500",
              cursor: "pointer",
              transition: "background-color 0.2s ease",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "#0056b3";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "#007bff";
            }}
          >
            🎮 Nuevo Juego
          </button>
          <button
            onClick={onClose}
            style={{
              backgroundColor: "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "8px",
              padding: "12px 24px",
              fontSize: "16px",
              fontWeight: "500",
              cursor: "pointer",
              transition: "background-color 0.2s ease",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "#545b62";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "#6c757d";
            }}
          >
            ✕ Cerrar
          </button>
        </div>

        {/* Close button (X) */}
        <button
          onClick={onClose}
          style={{
            position: "absolute",
            top: "16px",
            right: "16px",
            backgroundColor: "transparent",
            border: "none",
            fontSize: "24px",
            cursor: "pointer",
            color: "#6c757d",
            width: "32px",
            height: "32px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            borderRadius: "50%",
            transition: "background-color 0.2s ease",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "#f8f9fa";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "transparent";
          }}
        >
          ✕
        </button>
      </div>
    </div>
  );
};
