import React from "react";
import type { ConversationState } from "../services/impl/ISpeechRecognitionService";

interface ConversationStateIndicatorProps {
  conversationState: ConversationState;
}

/**
 * Component that displays the current conversation state with visual indicators
 * 
 * Features:
 * - Real-time state indicators for microphone, AI, and conversation
 * - Clear visual feedback with icons and text
 * - Responsive layout
 */
export const ConversationStateIndicator: React.FC<ConversationStateIndicatorProps> = ({ 
  conversationState 
}) => {
  /**
   * Get microphone state display info
   */
  const getMicrophoneState = () => ({
    icon: conversationState === "listening" ? "🎤" : "🔇",
    text: conversationState === "listening" ? "Activo" : "Inactivo"
  });

  /**
   * Get AI state display info
   */
  const getAIState = () => ({
    icon: conversationState === "speaking" ? "🔊" : "🤖",
    text: conversationState === "speaking" ? "Hablando" : "Silenciosa"
  });

  /**
   * Get conversation state display info
   */
  const getConversationState = () => {
    const stateMap = {
      idle: { icon: "⭕", text: "Inactivo" },
      listening: { icon: "👂", text: "Escuchando" },
      processing: { icon: "⚙️", text: "Procesando" },
      speaking: { icon: "💬", text: "Hablando" }
    };
    
    return stateMap[conversationState] || { icon: "⭕", text: conversationState };
  };

  const micState = getMicrophoneState();
  const aiState = getAIState();
  const convState = getConversationState();

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        gap: "16px",
        marginBottom: "16px",
        padding: "12px",
        backgroundColor: "#f8f9fa",
        borderRadius: "8px",
        border: "1px solid #e9ecef",
      }}
    >
      {/* Microphone State */}
      <StateItem
        icon={micState.icon}
        label="Micrófono"
        value={micState.text}
      />

      {/* AI State */}
      <StateItem
        icon={aiState.icon}
        label="IA"
        value={aiState.text}
      />

      {/* Conversation State */}
      <StateItem
        icon={convState.icon}
        label="Estado"
        value={convState.text}
      />
    </div>
  );
};

/**
 * Individual state item component
 */
interface StateItemProps {
  icon: string;
  label: string;
  value: string;
}

const StateItem: React.FC<StateItemProps> = ({ icon, label, value }) => (
  <div
    style={{
      display: "flex",
      alignItems: "center",
      gap: "6px",
      fontSize: "12px",
      color: "#495057",
    }}
  >
    <span style={{ fontSize: "14px" }}>{icon}</span>
    <span>{label}: {value}</span>
  </div>
);
