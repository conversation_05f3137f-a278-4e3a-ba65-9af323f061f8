import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import { MovistarOttService } from '../services/MovistarOttService';

interface MovistarInfoProps {
  characterName: string;
  isVisible: boolean;
  onClose: () => void;
}

/**
 * Component to display Movistar+ content information for a character
 *
 * Features:
 * - Searches for content by actor name and title
 * - Modal-style display with overlay
 * - Loading states and error handling
 * - Responsive design with tabs for different search types
 */
export const MovistarInfo: React.FC<MovistarInfoProps> = ({
  characterName,
  isVisible,
  onClose
}) => {
  const [movistarResult, setMovistarResult] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const movistarService = new MovistarOttService();

  /**
   * Reset state when component becomes visible
   */
  useEffect(() => {
    if (isVisible && characterName) {
      fetchMovistarInfo();
    }
  }, [isVisible, characterName]);

  /**
   * Fetch Movistar+ information for the character using OTT service
   */
  const fetchMovistarInfo = async () => {
    if (!characterName.trim()) return;

    setLoading(true);
    setError('');
    setMovistarResult('');

    try {
      console.log(`🎬 Buscando contenido de Movistar+ para: ${characterName}`);

      // Inicializar el servicio
      await movistarService.init();

      // Enviar consulta al agente
      const result = await movistarService.sendToAgentFinalOnly(`Tan solo quiero una pelicula del personaje ${characterName}`);
      console.log('✅ Resultado del agente Movistar+:', result);

      setMovistarResult(result);

      console.log('✅ Búsqueda de Movistar+ completada');

    } catch (err) {
      console.error('❌ Error fetching Movistar+ info:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido al obtener información de Movistar+');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle retry action
   */
  const handleRetry = () => {
    setMovistarResult('');
    setError('');
    fetchMovistarInfo();
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '20px'
        }}
        onClick={onClose}
      >
        {/* Modal Content */}
        <div
          style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '24px',
            maxWidth: '800px',
            width: '100%',
            maxHeight: '80vh',
            overflowY: 'auto',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
            position: 'relative'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px',
            borderBottom: '1px solid #eee',
            paddingBottom: '15px'
          }}>
            <h3 style={{ margin: 0, color: '#333' }}>🎬 Contenido Movistar+ - {characterName}</h3>
            <button
              style={{
                background: 'none',
                border: 'none',
                fontSize: '24px',
                cursor: 'pointer',
                color: '#666',
                padding: '5px'
              }}
              onClick={onClose}
            >
              ✕
            </button>
          </div>

          {/* Content */}
          <div>
            {loading && (
              <div style={{
                textAlign: 'center',
                padding: '40px 20px',
                color: '#666'
              }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  border: '4px solid #f3f3f3',
                  borderTop: '4px solid #007bff',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite',
                  margin: '0 auto 20px'
                }}></div>
                <p>Buscando contenido en Movistar+...</p>
              </div>
            )}

            {error && (
              <div style={{
                textAlign: 'center',
                padding: '40px 20px',
                color: '#dc3545'
              }}>
                <p style={{ marginBottom: '20px' }}>❌ {error}</p>
                <button
                  onClick={handleRetry}
                  style={{
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    padding: '10px 20px',
                    borderRadius: '5px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  🔄 Reintentar
                </button>
              </div>
            )}

            {!loading && !error && movistarResult && (
              <div>
                <h4 style={{ color: '#333', marginBottom: '15px' }}>🎬 Información de Movistar+:</h4>
                <div style={{
                  backgroundColor: '#f8f9fa',
                  padding: '15px',
                  borderRadius: '5px',
                  border: '1px solid #e9ecef',
                  maxHeight: '400px',
                  overflowY: 'auto',
                  fontFamily: 'inherit',
                  fontSize: '14px',
                  lineHeight: '1.5',
                  color: '#212529'
                }}>
                  <ReactMarkdown
                    components={{
                      h1: ({ children }) => (
                        <h1 style={{ color: '#333', margin: '20px 0 15px 0', fontSize: '20px', fontWeight: 'bold' }}>
                          {children}
                        </h1>
                      ),
                      h2: ({ children }) => (
                        <h2 style={{ color: '#333', margin: '18px 0 12px 0', fontSize: '18px', fontWeight: 'bold' }}>
                          {children}
                        </h2>
                      ),
                      h3: ({ children }) => (
                        <h3 style={{ color: '#333', margin: '16px 0 10px 0', fontSize: '16px', fontWeight: 'bold' }}>
                          {children}
                        </h3>
                      ),
                      img: ({ src, alt }) => (
                        <img
                          src={src}
                          alt={alt}
                          style={{
                            maxWidth: '100%',
                            height: 'auto',
                            margin: '10px 0',
                            borderRadius: '8px',
                            display: 'block'
                          }}
                        />
                      ),
                      a: ({ href, children }) => (
                        <a
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ color: '#007bff', textDecoration: 'none' }}
                        >
                          {children}
                        </a>
                      ),
                      strong: ({ children }) => (
                        <strong style={{ fontWeight: 'bold', color: '#333' }}>
                          {children}
                        </strong>
                      ),
                      p: ({ children }) => (
                        <p style={{ margin: '8px 0', lineHeight: '1.5' }}>
                          {children}
                        </p>
                      ),
                      ul: ({ children }) => (
                        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                          {children}
                        </ul>
                      ),
                      li: ({ children }) => (
                        <li style={{ margin: '4px 0' }}>
                          {children}
                        </li>
                      )
                    }}
                  >
                    {movistarResult}
                  </ReactMarkdown>
                </div>
              </div>
            )}

            {!loading && !error && !movistarResult && (
              <div style={{
                textAlign: 'center',
                padding: '40px 20px',
                color: '#666'
              }}>
                <p style={{ marginBottom: '20px' }}>No se encontraron resultados para "{characterName}"</p>
                <button
                  onClick={handleRetry}
                  style={{
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    padding: '10px 20px',
                    borderRadius: '5px',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  🔄 Buscar de nuevo
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
