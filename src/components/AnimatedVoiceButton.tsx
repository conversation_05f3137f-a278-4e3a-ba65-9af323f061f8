import React from "react";

interface AnimatedVoiceButtonProps {
  state: string;
  isActive: boolean;
  onClick: () => void;
  disabled?: boolean;
}

/**
 * Animated voice button component that changes appearance based on conversation state
 *
 * Features:
 * - Dynamic styling based on conversation state (listening, processing, speaking)
 * - CSS animations for visual feedback
 * - Hover effects for better UX
 */
export const AnimatedVoiceButton: React.FC<AnimatedVoiceButtonProps> = ({
  state,
  isActive,
  onClick,
  disabled = false
}) => {
  /**
   * Get button configuration based on current state
   * Returns icon, text, colors, and animation properties
   */
  const getButtonConfig = () => {
    // Handle disabled state first
    if (disabled) {
      return {
        icon: "🔇",
        text: "Juego Finalizado",
        color: "#6c757d",
        bgColor: "#f8f9fa",
        borderColor: "#dee2e6",
      };
    }

    if (!isActive) {
      return {
        icon: "🎤",
        text: "Iniciar Conversación",
        color: "#28a745",
        bgColor: "#d4edda",
        borderColor: "#c3e6cb",
      };
    }

    switch (state) {
      case "listening":
        return {
          icon: "🎤",
          text: "Escuchando...",
          color: "#28a745",
          bgColor: "#d4edda",
          borderColor: "#c3e6cb",
          pulse: true,
        };
      case "processing":
        return {
          icon: "⚙️",
          text: "Procesando...",
          color: "#ffc107",
          bgColor: "#fff3cd",
          borderColor: "#ffeaa7",
          spin: true,
        };
      case "speaking":
        return {
          icon: "🔊",
          text: "IA hablando...",
          color: "#17a2b8",
          bgColor: "#d1ecf1",
          borderColor: "#bee5eb",
          bounce: true,
        };
      default:
        return {
          icon: "⭕",
          text: "Activo",
          color: "#6c757d",
          bgColor: "#e2e3e5",
          borderColor: "#d6d8db",
        };
    }
  };

  const config = getButtonConfig();

  return (
    <>
      <button
        onClick={disabled ? undefined : onClick}
        disabled={disabled}
        style={{
          display: "flex",
          alignItems: "center",
          gap: "12px",
          padding: "16px 24px",
          backgroundColor: config.bgColor,
          color: config.color,
          border: `2px solid ${config.borderColor}`,
          borderRadius: "50px",
          cursor: disabled ? "not-allowed" : "pointer",
          fontSize: "16px",
          fontWeight: "bold",
          boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
          transition: "all 0.3s ease",
          opacity: disabled ? 0.6 : 1,
          // ...(!disabled),
        }}
        onMouseOver={disabled ? undefined : (e) => {
          e.currentTarget.style.transform = "translateY(-2px)";
          e.currentTarget.style.boxShadow = "0 6px 20px rgba(0,0,0,0.15)";
        }}
        onMouseOut={disabled ? undefined : (e) => {
          e.currentTarget.style.transform = "translateY(0)";
          e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.1)";
        }}
      >
        <span style={{ fontSize: "20px" }}>{config.icon}</span>
        <span>{config.text}</span>
      </button>
    </>
  );
};
