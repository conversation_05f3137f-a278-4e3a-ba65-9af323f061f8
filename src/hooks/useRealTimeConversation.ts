import { useState, useEffect, useCallback, useRef } from "react";
import { SpeechRecognitionService } from "../services/SpeechRecognitionService";
import { AppService } from "../services/AppService";
import { ConversationStorage } from "../services/ConversationStorage";
import type {
  ConversationState,
  SpeechRecognitionResult,
} from "../services/impl/ISpeechRecognitionService";
import {
  useConversationState,
  useIsActive,
  useConversationError,
  useConversationActions,
  useConversationStore
} from "../stores/conversationStore";
import { conversationStateManager } from "../services/ConversationStateManager";

/**
 * Custom hook for managing real-time voice conversations
 *
 * This hook provides a complete interface for voice-based conversations including:
 * - Speech recognition and synthesis
 * - Message history management
 * - Conversation state management
 * - Error handling
 * - Auto-activation features
 */

// Import conversation utilities
import {
  type ConversationMessage,
  convertStoredToConversation,
  extractResponseText,
  shouldIgnoreTranscript,
  createConversationMessage,
  filterInterimMessages,
  isValidMessageContent
} from "../utils/conversationUtils";

export interface UseRealTimeConversationReturn {
  // Estado
  isActive: boolean;
  conversationState: ConversationState;
  messages: ConversationMessage[];
  currentUserInput: string;
  isSupported: boolean;
  error: string | null;

  // Controles
  startConversation: () => Promise<boolean>;
  stopConversation: () => void;
  sendMessage: (message: string) => Promise<void>;
  clearMessages: () => void;
  addInitialMessage: (content: string) => void; // 👈 Nuevo método

  // Configuración
  enableSmartMicrophone: () => void;
  disableSmartMicrophone: () => void;
}

/**
 * Hook for managing real-time voice conversations with AI
 *
 * @param generatedCharacter - The character description for context
 * @param isGameStarted - Whether the game has started
 * @returns Object with conversation state and control functions
 */

export const useRealTimeConversation = (
  generatedCharacter?: string,
  isGameStarted?: boolean,
  gameFinished?: boolean
): UseRealTimeConversationReturn => {
  // Use centralized store for conversation state
  const conversationState = useConversationState();
  const isActive = useIsActive();
  const error = useConversationError();
  const { setConversationState, setIsActive, setError } = useConversationActions();

  // Keep local state for messages and user input (these are UI-specific)
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [currentUserInput, setCurrentUserInput] = useState("");

  const speechService = useRef(SpeechRecognitionService.getInstance());
  const appService = useRef(AppService.getInstance());
  const storage = useRef(ConversationStorage.getInstance());
  const interimMessageId = useRef<string | null>(null);

  const isSupported = speechService.current.isSupported();

  // Initialize ConversationStateManager with current state
  useEffect(() => {
    conversationStateManager.setConversationState(conversationState);
    conversationStateManager.setIsActive(isActive);
    conversationStateManager.setCanAutoActivate(!isActive && Boolean(isGameStarted) && isSupported);
  }, [conversationState, isActive, isGameStarted, isSupported]);

  // Set up activation handlers for ConversationStateManager
  useEffect(() => {
    conversationStateManager.setActivationHandlers(
      async () => {
        const success = await startConversation();
        if (success) {
          speechService.current.enableSmartMicrophoneControl();
          // Activate microphone after a delay
          setTimeout(() => {
            speechService.current.startListening();
          }, 1000);
        }
        return success;
      },
      () => {
        if (speechService.current.shouldBeListening() && !speechService.current.isListening()) {
          speechService.current.startListening();
        }
      }
    );
  }, []);

  // Cargar mensajes desde localStorage al inicializar
  useEffect(() => {
    const loadStoredMessages = () => {
      const session = storage.current.loadLatestSession();
      if (session && session.messages.length > 0) {
        const convertedMessages = session.messages.map(
          convertStoredToConversation
        );
        setMessages(convertedMessages);
        // console.log(
        //   "📖 Mensajes cargados desde localStorage:",
        //   convertedMessages.length
        // );
      }
    };

    loadStoredMessages();
  }, []);

  // Crear nueva sesión cuando cambia el personaje generado
  useEffect(() => {
    if (generatedCharacter) {
      storage.current.createNewSession(generatedCharacter);
      // console.log("🆕 Nueva sesión creada para personaje:", generatedCharacter);
    }
  }, [generatedCharacter]);

  // Agregar mensaje inicial de la IA (seed message)
  const addInitialMessage = useCallback((content: string) => {
    try {
      const seedMessage = storage.current.addSeedMessage(content);
      const convertedMessage = convertStoredToConversation(seedMessage);

      // Verificar si ya existe este mensaje en el estado
      setMessages((prev) => {
        const existingSeed = prev.find((m) => m.id === "seed-1");
        if (existingSeed) {
          return prev; // Ya existe, no agregar duplicado
        }
        return [convertedMessage, ...prev]; // Agregar al inicio
      });

      // console.log("🌱 Mensaje inicial agregado al storage y estado");
    } catch (error) {
      console.error("❌ Error agregando mensaje inicial:", error);
    }
  }, []);

  /**
   * Add message to conversation history
   * Handles both interim (temporary) and final messages
   */
  const addMessage = useCallback(
    (type: "user" | "ai", content: string, isInterim = false) => {
      if (!isValidMessageContent(content)) return "";

      const newMessage = createConversationMessage(type, content, isInterim);

      if (isInterim) {
        // Temporary message - only in state, not in storage
        interimMessageId.current = newMessage.id;
        setMessages((prev) => [...filterInterimMessages(prev, type), newMessage]);
      } else {
        // Final message - add to both storage and state
        try {
          const role = type === "user" ? "user" : "assistant";
          const storedMessage = storage.current.addMessage(role, content);
          const convertedMessage = convertStoredToConversation(storedMessage);

          setMessages((prev) => [...filterInterimMessages(prev, type), convertedMessage]);
          interimMessageId.current = null;
        } catch (error) {
          console.error("❌ Error guardando mensaje:", error);
          // Fallback: add only to state
          setMessages((prev) => [...filterInterimMessages(prev, type), newMessage]);
        }
      }

      return newMessage.id;
    },
    []
  );

  // Finalizar mensaje interim
  const finalizeMessage = useCallback(
    (content: string) => {
      if (interimMessageId.current) {
        // Reemplazar el mensaje interim con uno final
        addMessage("user", content, false);
      }
    },
    [addMessage]
  );

  /**
   * Send message to AI and handle response
   */
  const sendMessage = useCallback(
    async (message: string) => {
      if (!isValidMessageContent(message)) return;

      try {
        setConversationState("processing");
        speechService.current.setConversationState("processing");

        const response = await appService.current.generateWithIaVsPlayer(
          message,
          generatedCharacter
        );
        const responseText = extractResponseText(response);

        // Add AI response to history
        addMessage("ai", responseText);

        // Change state to "speaking"
        setConversationState("speaking");
        speechService.current.setConversationState("speaking");
      } catch (error) {
        console.error("❌ Error procesando mensaje:", error);
        setError("Error al procesar el mensaje");
        speechService.current.setConversationState("idle");
      }
    },
    [generatedCharacter, addMessage, setConversationState, setError]
  );

  /**
   * Handle speech recognition results
   * Processes both interim and final transcripts
   */
  const handleSpeechResult = useCallback(
    (result: SpeechRecognitionResult) => {
      const { transcript, isFinal } = result;

      if (!isValidMessageContent(transcript)) return;

      // Ignore transcripts that seem to be AI audio echo
      if (shouldIgnoreTranscript(transcript)) {
        console.log("🚫 Ignorando transcripción que parece ser eco del audio de la IA");
        return;
      }

      if (isFinal) {
        finalizeMessage(transcript);
        setCurrentUserInput("");
        sendMessage(transcript);
      } else {
        addMessage("user", transcript, true);
        setCurrentUserInput(transcript);
      }
    },
    [finalizeMessage, sendMessage, addMessage]
  );

  // Manejar cambios de estado
  const handleStateChange = useCallback((change: any) => {
    setConversationState(change.state);
  }, []);

  // Manejar errores
  const handleError = useCallback((errorMessage: string) => {
    setError(errorMessage);
    speechService.current.setConversationState("idle");
  }, [setError]);

  // Iniciar conversación
  const startConversation = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      const errorMsg =
        "El reconocimiento de voz no está soportado en este navegador";
      setError(errorMsg);
      return false;
    }

    // Don't start conversation if game has finished
    if (gameFinished) {
      console.log('🔇 Game has finished, conversation cannot be started');
      setError("El juego ha terminado");
      return false;
    }

    setError(null);
    setIsActive(true);

    const success = await speechService.current.startListening();
    if (!success) {
      setIsActive(false);
      const errorMsg = "No se pudo iniciar el reconocimiento de voz";
      setError(errorMsg);
      return false;
    }

    // return true;
    return success;
  }, [isSupported, gameFinished]);

  /**
   * Handle audio finished - reactivate microphone for continued conversation
   */
  const handleAudioFinished = useCallback(() => {
    console.log("🎤 Audio finished callback ejecutado");

    // Change state when audio ends
    console.log("🎤 Cambiando estado a idle tras finalización de audio...");
    setConversationState("idle");
    speechService.current.setConversationState("idle");

    // Auto-reactivate microphone if conversation is active and smart microphone is enabled
    const store = useConversationStore.getState();
    if (isActive && store.smartMicrophoneEnabled && !gameFinished) {
      console.log("🎤 Reactivando micrófono automáticamente tras finalización de audio...");
      setTimeout(() => {
        if (isActive && !speechService.current.isListening() && !gameFinished) {
          speechService.current.startListening();
        }
      }, 500); // Small delay to ensure audio has fully stopped
    } else if (gameFinished) {
      console.log("🔇 Game finished, not reactivating microphone");
    }
  }, [isActive, setConversationState]);

  const handleAudioStarted = useCallback(() => {
    console.log("🔊 Audio started callback ejecutado");

    // Detener micrófono si está activo
    if (speechService.current.isListening()) {
      console.log("🔇 Deteniendo micrófono: audio iniciado");
      speechService.current.stopListening();
    }

    // Cambiar estados
    setConversationState("speaking");
    speechService.current.setConversationState("speaking");
  }, []);

  // Inicializar callbacks del servicio de voz
  useEffect(() => {
    speechService.current.onResult(handleSpeechResult);
    speechService.current.onStateChange(handleStateChange);
    speechService.current.onError(handleError);

    // Configurar callbacks de audio
    appService.current.setAudioFinishedCallback(handleAudioFinished);
    appService.current.setAudioStartedCallback(handleAudioStarted);
  }, [
    handleSpeechResult,
    handleStateChange,
    handleError,
    handleAudioFinished,
    handleAudioStarted,
  ]);

  // Detener conversación
  const stopConversation = useCallback(() => {
    speechService.current.stopListening();
    setIsActive(false);
    speechService.current.setConversationState("idle");
    setCurrentUserInput("");
  }, [setIsActive]);

  // Limpiar mensajes
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentUserInput("");
    setError(null);
    storage.current.clearAllConversations();
    console.log("🗑️ Historial de mensajes y todas las conversaciones limpiadas");
  }, []);

  // Control inteligente del micrófono
  const enableSmartMicrophone = useCallback(() => {
    speechService.current.enableSmartMicrophoneControl();
  }, []);

  const disableSmartMicrophone = useCallback(() => {
    speechService.current.disableSmartMicrophoneControl();
  }, []);

  // Cleanup al desmontar
  useEffect(() => {
    return () => {
      if (isActive) {
        stopConversation();
      }
    };
  }, [isActive, stopConversation]);

  return {
    // Estado
    isActive,
    conversationState,
    messages,
    currentUserInput,
    isSupported,
    error,

    // Controles
    startConversation,
    stopConversation,
    sendMessage,
    clearMessages,
    addInitialMessage, // 👈 Nuevo método expuesto

    // Configuración
    enableSmartMicrophone,
    disableSmartMicrophone,
  };
};
