.primary-button {
  box-shadow: 0px 0px 12px 0px #88ffd5;
  background-color: #88FFD5;
  border-radius: 0.5rem;
  color: black;
}

// Reset section styles
.reset-section {
  display: flex;
  justify-content: center;
  margin-top: 16px;

  .reset-button {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #c82333;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// SimpleVoiceChat component styles
.character-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.unsupported-browser-warning {
  background-color: #fff3cd;
  border: 2px solid #ffc107;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  text-align: center;

  h4 {
    color: #856404;
    margin-bottom: 8px;
  }

  p {
    color: #856404;
    margin: 0;
  }
}