{"name": "genigma-wb", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build-tsc": "tsc -b && vite build", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "tree": "tree -a --prune -I 'node_modules|lib|dist|.git|.firebase|backup_*' > docs/file-system.md"}, "dependencies": {"axios": "^1.11.0", "microapps": "github:Telefonica/microapps-library", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "sass-embedded": "^1.92.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}